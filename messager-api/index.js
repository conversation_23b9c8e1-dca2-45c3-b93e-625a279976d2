require("dotenv").config({});
const axios = require("axios").default;
const URL = 'https://graph.facebook.com/v17.0/107582595774845/messages';
const accessToken = process.env.FACEBOOK_PAGE_ACCESS_TOKEN;
const express = require('express');
const app = express();
app.use(express.json());

const messageData = {
  recipient: {
    id: '6202329483228260'
  },
  message: {
    text: 'Hello, this is a test message!'
  },
  messaging_type: 'RESPONSE',
  access_token: accessToken
};

app.post('/webhook', (req, res) => {
  const body = req.body;
  console.log("call cc gif")
  // Verify the webhook event
  if (body.object === 'page') {
    body.entry.forEach((entry) => {
      const webhookEvent = entry.messaging[0];
      const senderPSID = webhookEvent.sender.id; // Extract PSID
      
      // Process received message and send a response
      const messageText = 'Hello, I received your message!';
      console.log(senderPSID, "senderPSID");
    });

    res.status(200).send('EVENT_RECEIVED');
    return;
  }

  res.status(200).send('ok');
});

app.get('/webhook', (req, res) => {
  
  // Parse the query params
  let mode = req.query["hub.mode"];
  let token = req.query["hub.verify_token"];
  let challenge = req.query["hub.challenge"];

  // Check if a token and mode is in the query string of the request
  if (mode && token) {
    // Check the mode and token sent is correct
    if (mode === "subscribe" && token === "test") {
      // Respond with the challenge token from the request
      console.log("WEBHOOK_VERIFIED");
      res.status(200).send(challenge);
    } else {
      // Respond with '403 Forbidden' if verify tokens do not match
      res.sendStatus(403);
    }
  }
});

const sendMessage = async () => {
  try {
    const res = await axios.post(URL, messageData, {
      headers: { 'Content-Type': 'application/json' }
    })
  
    console.log(res)
  } catch (err) {
    console.log(err, "err")
  }

}

const getUserPSID = async () => {
  try {
    const response = await axios.get(
      `https://graph.facebook.com/v17.0/107582595774845/me&access_token=${accessToken}`
    );

    console.log(response.data, "response")
    const userPSID = response.data.ids_for_pages.data[0].id;
    console.log(userPSID, "userPSID")
    return userPSID;
  } catch (error) {
    console.error('Error fetching user PSID:', error);
    return null;
  }
};

app.listen(3000, () => {
  console.log(`Server is listening on port ${3000}`);
});

sendMessage()