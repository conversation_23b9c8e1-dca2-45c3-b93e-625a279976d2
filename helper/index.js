const BigNumber = require('bignumber.js');

/**
 * @DESC Bignumber config init for all instance
 * */
BigNumber.config({DECIMAL_PLACES: 1000, EXPONENTIAL_AT: [-1000, 1000], ROUNDING_MODE: 3});

/**
 * @DESC instead import from bignumber.js
 * we should import im our service,
 * and all function will be the same https://www.npmjs.com/package/bignumber.js
 * */
const _BigNumber = BigNumber;

/**
 * @DESC instead use `let number = new BigNumber(val)`
 * we can use like `let number = BigNumberInstance(val)`
 * */
const BigNumberInstance = (value) => new _BigNumber(value);


const orderByKey = (array, key, sortBy) => {
  if (!array?.length) {
    return;
  }

  let swapped;
  const compareFunctionName = sortBy === 'desc' ? 'isLessThan' : 'isGreaterThan';

  do {
    swapped = false;

    for (let i = 0; i < array.length - 1; i++) {
      if (BigNumberInstance(array[i][key])[compareFunctionName](array[i + 1][key])) {
        let temp = array[i];
        array[i] = array[i + 1];
        array[i + 1] = temp;
        swapped = true;
      }
    }
  } while (swapped);

  return array;
};

const handleGetCoinAmount = async (
  amount,
  account,
  coinType,
  inheritTx,
) => {
  const {objectIds, balance} = await getCoinOjectIdsByAmount(account, BigInt(amount), coinType);

  //handle merge and split other coins
  let coinObjectId = objectIds[0];
  if (objectIds.length > 1) {
    inheritTx.mergeCoins(
      inheritTx.object(coinObjectId),
      objectIds.slice(1).map((item) => inheritTx.object(item)),
    );
  }

  const splitAmount = BigNumberInstance(balance).minus(amount).toFixed();

  if (BigNumberInstance(splitAmount).isGreaterThan(0)) {
    // split correct amount to swap
    const [coin] = inheritTx.splitCoins(inheritTx.object(coinObjectId), [inheritTx.pure(splitAmount)]);
    inheritTx.transferObjects([coin], inheritTx.pure(account));
  }

  return {tx, coin: coinObjectId};
};

const getCoinOjectIdsByAmount = async (
  address,
  amount,
  coinType,
  provider
) => {
  const coins = await provider.getCoins({owner: address, coinType});
  //sort coin balance before get object id
  const coinBalances = orderByKey(
    coins.data.map((item) => {
      return {
        ...item,
        balance: item.balance,
      };
    }),
    'balance',
    'desc',
  );

  let balance = '0';
  let objectIds = [];
  for (const coin of coinBalances) {
    balance = BigNumberInstance(coin.balance).plus(balance).toFixed();
    objectIds.push(coin.coinObjectId);

    if (BigNumberInstance(balance).isGreaterThanOrEqualTo(amount.toString())) {
      break;
    }
  }

  return {objectIds, balance};
};

async function sleep(time) {
  return new Promise(resolve => setTimeout(resolve, time));
}

module.exports = {
  BigNumberInstance,
  orderByKey,
  handleGetCoinAmount,
  getCoinOjectIdsByAmount,
  sleep
}