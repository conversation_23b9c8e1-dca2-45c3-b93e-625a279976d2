const MongoClient = require("mongodb").MongoClient;


async function init() {
  const client = new MongoClient("mongodb+srv://hoangnamhr:<EMAIL>/base-db?retryWrites=true&w=majority", { useNewUrlParser: true, useUnifiedTopology: true});
  await client.connect();
  const db = client.db("twitter-data");
  const collection = db.collection("twitter-data");

  const { docId } = "docs";
  const data = {
    user: "nam"
  };

  try {
    await collection.updateOne({ _id: docId }, { $set: data }, { upsert: true});
  } catch (error) {
    console.error(error);
  } finally {
    await client.close();
  }
}


init();