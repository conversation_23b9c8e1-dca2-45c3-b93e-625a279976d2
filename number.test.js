const { default: BigNumber } = require('bignumber.js')
var mocha = require('mocha')
var describe = mocha.describe
var it = mocha.it
var assert = require('chai').assert

// function formatNumber(number, decimal) {
//   number = BigNumber(number).toString()
//   if (isNaN(String(number).replace(',', ''))) {
//     return '0';
//   }
//   // Convert the number to a string and split it into integer and decimal parts

//   let [integer, decimalPart] = String(number).split('.');
  
//   // Format the integer part with commas
//   const formattedInteger = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  
//   // If there is no decimal part, return the formatted integer
//   if (!decimal) {
//     return formattedInteger;
//   }
  
//   // If there is a decimal part, format it to the desired number of decimal places
//   const formattedDecimal = decimalPart ? decimalPart.slice(0, decimal) : '';
//   return `${formattedInteger}.${formattedDecimal}`;
// }


// describe('formatNumber', function() {
//   it('should format integers with commas', function() {
//     assert.equal(formatNumber(1000), '1,000');
//     assert.equal(formatNumber('1,000000'), '1,000,000');
//     assert.equal(formatNumber(123456789), '123,456,789');
//   });

//   it('should format numbers with decimals to the specified precision', function() {
//     assert.equal(formatNumber(1.2345, 4), '1.2345');
//     assert.equal(formatNumber(1.23456789, 6), '1.234567');
//     assert.equal(formatNumber(1.2, 0), '1');
//   });

//   it('should format numbers with trailing zeroes in decimal part', function() {
//     assert.equal(formatNumber(1.23000, 3), '1.23');
//     assert.equal(formatNumber(1.20, 2), '1.2');
//     assert.equal(formatNumber(1.200, 1), '1.2');
//   });

//   it('should format negative numbers', function() {
//     assert.equal(formatNumber(-1000), '-1,000');
//     assert.equal(formatNumber(-1.2345, 3), '-1.234');
//   });

//   it('should return an empty string for non-numeric input', function() {
//     assert.equal(formatNumber('abc'), '0');
//     assert.equal(formatNumber(undefined), '0');
//   });
  
//   it('small number', function() {
//     assert.equal(formatNumber('0.0000000201', 10), '0.0000000201');
//   });
// });

function getTotalDecimalPlaces(number) {
  const decimalString = number.toString();
  const decimalIndex = decimalString.indexOf('.');
  
  if (decimalIndex === -1) {
    return 0; // No decimal places
  } else {
    return decimalString.length - decimalIndex - 1;
  }
}


describe('getTotalDecimalPlaces', function () {
  it('should return the correct number of decimal places', function () {
    assert.strictEqual(getTotalDecimalPlaces(3.14159), 5);
    assert.strictEqual(getTotalDecimalPlaces(10.0), 0);
    assert.strictEqual(getTotalDecimalPlaces(42), 0);
    assert.strictEqual(getTotalDecimalPlaces(1.23), 2);
    assert.strictEqual(getTotalDecimalPlaces(0.001), 3);
  });

  it('should handle negative numbers', function () {
    assert.strictEqual(getTotalDecimalPlaces(-5.25), 2);
    assert.strictEqual(getTotalDecimalPlaces(-100.0), 0);
    assert.strictEqual(getTotalDecimalPlaces(-0.12345), 5);
  });
});