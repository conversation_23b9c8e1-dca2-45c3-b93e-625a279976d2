const axios = require("axios").default;
const { sleep } = require("../../helper");
const { createAbstractClient } = require("@abstract-foundation/agw-client");
const { http } = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { abstract } = require("viem/chains");
require("dotenv").config({});

const privateKey = process.env.ABS_PRIVATE_KEYS.split(",");
const absAccounts = [
  // {
  //   privateKey:
  //     "0xc8779801273cb9e0a2501d863ee5ea5cbf89609cab9686224277b77426b30fc5",
  //   address: "0x23C570c752C456D0b5F396b0d2Dd24A1E773008D",
  // },
  {
    privateKey: privateKey[0],
    address: "0xdE5c10e07F0BB5db8F48a2f67b38A0C23e24afe4",
  },
  {
    privateKey: privateKey[1],
    address: "0x81075e2f2fAF575C474446fc740d25a91c1C8086",
  },
  {
    privateKey: privateKey[2],
    address: "0x534712F664208C8d13bea22bd67432576E7f047f",
  },
  {
    privateKey: privateKey[3],
    address: "0x6c55672690D2084EF951558Ae8befCD8d4790DE6",
  },
  {
    privateKey:
      "0xf5d994961486ae75c5345763e285775ae4b4842b7a9b3851c61f4c409488fe51",
    address: "0x0C27837A3a6364f9C9f8C534dbE26941f1B9DfA5",
  },
];

class Gigaverse {
  constructor() {
    this.dungeonId = 0;
    this.url = "https://gigaverse.io/api/game/dungeon/action";
    this.player = {};
    this.enemy = {};
    this.actions = ["rock", "paper", "scissor"];
    this.accessToken = "";

    this.start();
  }

  async joinDungeon() {
    try {
      let action = "start_run";
      console.log(
        {
          action,
          actionToken: "",
          dungeonId: 1,
        },
        "joinDungeon body"
      );
      const data = await axios.default({
        method: "post",
        url: this.url,
        headers: {
          "content-type": "application/json",
          authorization: `Bearer ${this.accessToken}`,
        },
        data: {
          action,
          actionToken: "",
          dungeonId: 1,
          data: {
            consumables: [],
            itemId: 0,
            index: 0,
          },
        },
      });

      console.log("join the dungeon success");
      await sleep(5000);
      await this.attack();
    } catch (error) {
      console.log(error?.response?.data ?? error, "join the dungeon error");

      // if (error?.response?.data?.message == "Error handling action") {
      //   console.log("here");
      //   await sleep(5000);
      //   await this.attack();
      // }
    }
  }

  async attack(action, isEmptyActionToken = false) {
    let _action = action ?? "rock";

    try {
      console.log("attacking...");
      if (this.player._id) {
        _action = this.actions.find((item) => {
          const actionData = this.player[item];

          if (actionData.currentCharges >= 1 && item !== this.player.lastMove) {
            return true;
          }
        });

        if (!_action) {
          _action = this.player.lastMove;
        }
      }
      console.log(
        {
          action: _action,
          actionToken: isEmptyActionToken ? "" : new Date().valueOf(),
          dungeonId: this.dungeonId,
        },
        " attack body"
      );
      const data = await axios.default({
        method: "post",
        url: this.url,
        headers: {
          "content-type": "application/json",
          authorization: `Bearer ${this.accessToken}`,
        },
        data: {
          action: _action,
          actionToken: isEmptyActionToken ? "" : new Date().valueOf(),
          dungeonId: this.dungeonId,
          data: {
            consumables: [],
            itemId: 0,
            index: 0,
          },
        },
      });

      this.player = data.data.data.run.players[0];
      this.enemy = data.data.data.run.players[1];
      console.log(this.player, "attack success with result");
      if (this.player.health.current == 0) {
        console.log("loss, replay.....");
        await sleep(5000);
        await this.joinDungeon();
        return;
      }

      if (this.enemy.health.current == 0) {
        console.log("join the next level....");
        await sleep(5000);
        await this.joinNextLevel();
        return;
      }
      await sleep(5000);
      console.log("attack success");
      await this.attack();
    } catch (error) {
      const newAction = this.actions.find((item) => item !== _action);
      console.log(error?.response?.data?.message ?? error, "attack error");
      if (
        error?.response?.data?.message ==
        "Error tracking action: Error: Invalid action token"
      ) {
        await this.attack(newAction, true);
        return;
      }

      await this.attack(newAction);
    }
  }

  async joinNextLevel() {
    try {
      console.log("joining next level...");
      const data = await axios.default({
        method: "post",
        url: this.url,
        headers: {
          "content-type": "application/json",
          authorization: `Bearer ${this.accessToken}`,
        },
        data: {
          action: "loot_two",
          actionToken: "",
          dungeonId: this.dungeonId,
          data: {
            consumables: [],
            itemId: 0,
            index: 0,
          },
        },
      });
      console.log(data, "data");
      this.player = data.data.data.run.players[0];
      this.enemy = data.data.data.run.players[1];

      await sleep(6000);
      await this.attack();

      console.log("join next level success");
    } catch (e) {
      console.log("join next level error", e?.response?.data ?? e);
    }
  }

  async start() {
    console.log("starting...");
    await this.login();
    // await sleep(3000);
    // this.attack();
  }

  async login() {
    for (const absAccount of absAccounts) {
      try {
        const now = new Date().valueOf();
        const message = `Login to Gigaverse at ${now}`;
        const account = privateKeyToAccount(absAccount.privateKey);

        const agwClient = await createAbstractClient({
          chain: abstract,
          signer: account,
          transport: http(`https://api.mainnet.abs.xyz`),
        });

        const signature = await agwClient.signMessage({
          message,
        });
        console.log(
          {
            address: absAccount.address,
            message,
            signature,
            timestamp: now,
          },
          "login payload"
        );
        const data = await axios.default({
          method: "post",
          url: "https://gigaverse.io/api/user/auth",
          headers: {
            "content-type": "application/json",
            authorization: `Bearer ${this.accessToken}`,
          },
          data: {
            address: absAccount.address,
            message,
            signature,
            timestamp: now,
          },
        });
        this.accessToken = data.data.jwt;

        await this.joinDungeon();
      } catch (error) {
        console.log(error?.response?.data ?? error, "login error");
      }
    }
  }
}

function init() {
  new Gigaverse();
}
init();
setInterval(init, 1000 * 60 * 60 * 12);
