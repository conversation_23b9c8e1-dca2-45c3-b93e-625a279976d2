const { createAbstractClient } = require("@abstract-foundation/agw-client");
const { http, parseEther, isAddress, formatEther } = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { abstract } = require("viem/chains");

// Abstract blockchain configuration
const ABSTRACT_CONFIG = {
  rpcUrl: "https://api.mainnet.abs.xyz",
  chainId: 11124, // Abstract mainnet chain ID
  name: "Abstract",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18,
  },
};

// ERC-20 Token ABI for transfer function
const ERC20_ABI = [
  {
    inputs: [
      { name: "to", type: "address" },
      { name: "amount", type: "uint256" },
    ],
    name: "transfer",
    outputs: [{ name: "", type: "bool" }],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [{ name: "account", type: "address" }],
    name: "balanceOf",
    outputs: [{ name: "", type: "uint256" }],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "decimals",
    outputs: [{ name: "", type: "uint8" }],
    stateMutability: "view",
    type: "function",
  },
];

/**
 * Creates a wallet signer using a private key for the Abstract blockchain
 * @param {string} privateKey - The private key (with or without 0x prefix)
 * @returns {Object} - Object containing account and Abstract client
 */
async function createAbstractWallet(privateKey) {
  try {
    // Ensure private key has 0x prefix
    const formattedPrivateKey = privateKey.startsWith("0x")
      ? privateKey
      : `0x${privateKey}`;

    // Create account from private key
    const account = privateKeyToAccount(formattedPrivateKey);

    // Create Abstract client
    const agwClient = await createAbstractClient({
      chain: abstract,
      signer: account,
      transport: http(ABSTRACT_CONFIG.rpcUrl),
    });

    console.log(
      `✅ Wallet created successfully for address: ${account.address}`
    );

    return {
      account,
      client: agwClient,
      address: account.address,
    };
  } catch (error) {
    console.error("❌ Error creating Abstract wallet:", error.message);
    throw new Error(`Failed to create wallet: ${error.message}`);
  }
}

/**
 * Validates if an address is a valid Ethereum address
 * @param {string} address - The address to validate
 * @returns {boolean} - True if valid, false otherwise
 */
function validateAddress(address) {
  return isAddress(address);
}

/**
 * Gets the native ETH balance of an address
 * @param {Object} client - The Abstract client
 * @param {string} address - The address to check balance for
 * @returns {string} - Balance in ETH
 */
async function getETHBalance(client, address) {
  try {
    const balance = await client.getBalance({ address });
    return formatEther(balance);
  } catch (error) {
    console.error("❌ Error getting ETH balance:", error.message);
    throw error;
  }
}

/**
 * Gets the token balance of an address for a specific ERC-20 token
 * @param {Object} client - The Abstract client
 * @param {string} tokenAddress - The token contract address
 * @param {string} walletAddress - The wallet address to check balance for
 * @returns {Object} - Object containing balance and decimals
 */
async function getTokenBalance(client, tokenAddress, walletAddress) {
  try {
    // Get token decimals
    const decimals = await client.readContract({
      address: tokenAddress,
      abi: ERC20_ABI,
      functionName: "decimals",
    });

    // Get token balance
    const balance = await client.readContract({
      address: tokenAddress,
      abi: ERC20_ABI,
      functionName: "balanceOf",
      args: [walletAddress],
    });

    // Format balance based on token decimals
    const formattedBalance = Number(balance) / Math.pow(10, decimals);

    return {
      raw: balance.toString(),
      formatted: formattedBalance.toString(),
      decimals: decimals,
    };
  } catch (error) {
    console.error("❌ Error getting token balance:", error.message);
    throw error;
  }
}

/**
 * Estimates gas for a transaction
 * @param {Object} client - The Abstract client
 * @param {Object} transactionRequest - The transaction request object
 * @returns {bigint} - Estimated gas limit
 */
async function estimateGas(client, transactionRequest) {
  try {
    const gasEstimate = await client.estimateGas(transactionRequest);
    // Add 20% buffer to gas estimate
    const gasWithBuffer = (gasEstimate * 120n) / 100n;
    console.log(
      `⛽ Gas estimated: ${gasEstimate.toString()} (with buffer: ${gasWithBuffer.toString()})`
    );
    return gasWithBuffer;
  } catch (error) {
    console.error("❌ Error estimating gas:", error.message);
    throw error;
  }
}

/**
 * Transfers native ETH from the signer's wallet to a recipient address
 * @param {string} privateKey - The private key of the sender
 * @param {string} recipientAddress - The recipient's address
 * @param {string} amount - The amount of ETH to send (in ETH, e.g., "0.1")
 * @returns {Object} - Transaction result with hash and confirmation
 */
async function transferETH(privateKey, recipientAddress, amount) {
  try {
    console.log(`🚀 Starting ETH transfer...`);
    console.log(`📤 Amount: ${amount} ETH`);
    console.log(`📍 To: ${recipientAddress}`);

    // Validate recipient address
    if (!validateAddress(recipientAddress)) {
      throw new Error("Invalid recipient address format");
    }

    // Create wallet
    const wallet = await createAbstractWallet(privateKey);

    // Check sender balance
    const senderBalance = await getETHBalance(wallet.client, wallet.address);
    console.log(`💰 Sender balance: ${senderBalance} ETH`);

    if (parseFloat(senderBalance) < parseFloat(amount)) {
      throw new Error(
        `Insufficient balance. Available: ${senderBalance} ETH, Required: ${amount} ETH`
      );
    }

    // Prepare transaction
    const value = parseEther(amount);

    // Estimate gas
    const gasLimit = await estimateGas(wallet.client, {
      account: wallet.account,
      to: recipientAddress,
      value: value,
    });

    // Send transaction
    console.log(`📡 Sending transaction...`);
    const txHash = await wallet.client.sendTransaction({
      account: wallet.account,
      to: recipientAddress,
      value: value,
      gas: gasLimit,
    });

    console.log(`✅ Transaction sent! Hash: ${txHash}`);

    // Wait for confirmation
    console.log(`⏳ Waiting for transaction confirmation...`);
    const receipt = await wallet.client.waitForTransactionReceipt({
      hash: txHash,
      confirmations: 1,
    });

    console.log(`🎉 Transaction confirmed in block: ${receipt.blockNumber}`);
    console.log(`💸 Gas used: ${receipt.gasUsed.toString()}`);

    return {
      success: true,
      transactionHash: txHash,
      blockNumber: receipt.blockNumber.toString(),
      gasUsed: receipt.gasUsed.toString(),
      from: wallet.address,
      to: recipientAddress,
      amount: amount,
      type: "ETH",
    };
  } catch (error) {
    console.error("❌ ETH transfer failed:", error.message);
    return {
      success: false,
      error: error.message,
      from: null,
      to: recipientAddress,
      amount: amount,
      type: "ETH",
    };
  }
}

/**
 * Transfers ERC-20 tokens from the signer's wallet to a recipient address
 * @param {string} privateKey - The private key of the sender
 * @param {string} tokenAddress - The contract address of the ERC-20 token
 * @param {string} recipientAddress - The recipient's address
 * @param {string} amount - The amount of tokens to send (in token units, e.g., "100")
 * @returns {Object} - Transaction result with hash and confirmation
 */
async function transferToken(
  privateKey,
  tokenAddress,
  recipientAddress,
  amount
) {
  try {
    console.log(`🚀 Starting token transfer...`);
    console.log(`🪙 Token: ${tokenAddress}`);
    console.log(`📤 Amount: ${amount} tokens`);
    console.log(`📍 To: ${recipientAddress}`);

    // Validate addresses
    if (!validateAddress(recipientAddress)) {
      throw new Error("Invalid recipient address format");
    }
    if (!validateAddress(tokenAddress)) {
      throw new Error("Invalid token address format");
    }

    // Create wallet
    const wallet = await createAbstractWallet(privateKey);

    // Get token information and balance
    const tokenBalance = await getTokenBalance(
      wallet.client,
      tokenAddress,
      wallet.address
    );
    console.log(`💰 Sender token balance: ${tokenBalance.formatted} tokens`);

    if (parseFloat(tokenBalance.formatted) < parseFloat(amount)) {
      throw new Error(
        `Insufficient token balance. Available: ${tokenBalance.formatted} tokens, Required: ${amount} tokens`
      );
    }

    // Convert amount to token's decimal format
    const tokenAmount = BigInt(
      parseFloat(amount) * Math.pow(10, tokenBalance.decimals)
    );

    // Estimate gas for token transfer
    const gasLimit = await estimateGas(wallet.client, {
      account: wallet.account,
      to: tokenAddress,
      data: wallet.client.encodeFunctionData({
        abi: ERC20_ABI,
        functionName: "transfer",
        args: [recipientAddress, tokenAmount],
      }),
    });

    // Send token transfer transaction
    console.log(`📡 Sending token transfer transaction...`);
    const txHash = await wallet.client.writeContract({
      account: wallet.account,
      address: tokenAddress,
      abi: ERC20_ABI,
      functionName: "transfer",
      args: [recipientAddress, tokenAmount],
      gas: gasLimit,
    });

    console.log(`✅ Transaction sent! Hash: ${txHash}`);

    // Wait for confirmation
    console.log(`⏳ Waiting for transaction confirmation...`);
    const receipt = await wallet.client.waitForTransactionReceipt({
      hash: txHash,
      confirmations: 1,
    });

    console.log(`🎉 Transaction confirmed in block: ${receipt.blockNumber}`);
    console.log(`💸 Gas used: ${receipt.gasUsed.toString()}`);

    return {
      success: true,
      transactionHash: txHash,
      blockNumber: receipt.blockNumber.toString(),
      gasUsed: receipt.gasUsed.toString(),
      from: wallet.address,
      to: recipientAddress,
      amount: amount,
      tokenAddress: tokenAddress,
      type: "ERC20",
    };
  } catch (error) {
    console.error("❌ Token transfer failed:", error.message);
    return {
      success: false,
      error: error.message,
      from: null,
      to: recipientAddress,
      amount: amount,
      tokenAddress: tokenAddress,
      type: "ERC20",
    };
  }
}

// Example usage and test data
const EXAMPLE_CONFIG = {
  privateKeys: [
    "7cc098e6cb4d344f362f2651d2068dea1dece6e1f0a59aef45c1b74e8e5e2af6",
  ],
  recipientAddress: "******************************************",
  tokenAddresses: [
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
  ],
};

/**
 * Example function demonstrating how to use the token transfer functions
 */
async function exampleUsage() {
  try {
    console.log("🔥 Abstract Token Transfer Example");
    console.log("================================");

    const privateKey = EXAMPLE_CONFIG.privateKeys[0];
    const recipient = EXAMPLE_CONFIG.recipientAddress;

    // Example 1: Transfer ETH
    console.log("\n📍 Example 1: Transfer ETH");
    const ethResult = await transferETH(privateKey, recipient, "0.001");
    console.log("ETH Transfer Result:", ethResult);

    // Example 2: Transfer ERC-20 Token
    console.log("\n📍 Example 2: Transfer ERC-20 Token");
    const tokenAddress = EXAMPLE_CONFIG.tokenAddresses[0];
    const tokenResult = await transferToken(
      privateKey,
      tokenAddress,
      recipient,
      "10"
    );
    console.log("Token Transfer Result:", tokenResult);

    // Example 3: Check balances before transfer
    console.log("\n📍 Example 3: Check Balances");
    const wallet = await createAbstractWallet(privateKey);

    const ethBalance = await getETHBalance(wallet.client, wallet.address);
    console.log(`ETH Balance: ${ethBalance} ETH`);

    const tokenBalance = await getTokenBalance(
      wallet.client,
      tokenAddress,
      wallet.address
    );
    console.log(`Token Balance: ${tokenBalance.formatted} tokens`);
  } catch (error) {
    console.error("❌ Example failed:", error.message);
  }
}

/**
 * Batch transfer function for multiple recipients
 * @param {string} privateKey - The private key of the sender
 * @param {Array} transfers - Array of transfer objects {to, amount, tokenAddress?}
 * @returns {Array} - Array of transaction results
 */
async function batchTransfer(privateKey, transfers) {
  const results = [];

  console.log(
    `🚀 Starting batch transfer of ${transfers.length} transactions...`
  );

  for (let i = 0; i < transfers.length; i++) {
    const transfer = transfers[i];
    console.log(`\n📦 Processing transfer ${i + 1}/${transfers.length}`);

    try {
      let result;
      if (transfer.tokenAddress) {
        // ERC-20 token transfer
        result = await transferToken(
          privateKey,
          transfer.tokenAddress,
          transfer.to,
          transfer.amount
        );
      } else {
        // ETH transfer
        result = await transferETH(privateKey, transfer.to, transfer.amount);
      }

      results.push(result);

      // Add delay between transactions to avoid nonce issues
      if (i < transfers.length - 1) {
        console.log("⏳ Waiting 2 seconds before next transaction...");
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`❌ Transfer ${i + 1} failed:`, error.message);
      results.push({
        success: false,
        error: error.message,
        to: transfer.to,
        amount: transfer.amount,
        tokenAddress: transfer.tokenAddress || null,
      });
    }
  }

  console.log(
    `\n✅ Batch transfer completed. ${
      results.filter((r) => r.success).length
    }/${results.length} successful`
  );
  return results;
}

// Export functions for use in other modules
module.exports = {
  createAbstractWallet,
  validateAddress,
  getETHBalance,
  getTokenBalance,
  transferETH,
  transferToken,
  batchTransfer,
  exampleUsage,
  ABSTRACT_CONFIG,
  ERC20_ABI,
};

// Run example if this file is executed directly
if (require.main === module) {
  exampleUsage().catch(console.error);
}
