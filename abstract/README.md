# Abstract Blockchain Token Transfer

A comprehensive JavaScript implementation for transferring ETH and ERC-20 tokens on the Abstract blockchain using the Abstract Global Wallet (AGW) client.

## Features

- ✅ **Wallet Creation**: Create wallet signers from private keys
- ✅ **ETH Transfers**: Send native ETH with proper gas estimation
- ✅ **ERC-20 Token Transfers**: Send any ERC-20 token with automatic decimal handling
- ✅ **Address Validation**: Validate Ethereum addresses before transactions
- ✅ **Balance Checking**: Check ETH and token balances
- ✅ **Gas Estimation**: Automatic gas estimation with 20% buffer
- ✅ **Transaction Confirmation**: Wait for transaction confirmation
- ✅ **Batch Transfers**: Send multiple transactions in sequence
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **Example Usage**: Complete examples and test scripts

## Installation

The required dependencies are already included in the project:

```bash
npm install @abstract-foundation/agw-client viem
```

## Quick Start

### 1. Basic ETH Transfer

```javascript
const { transferETH } = require('./transferToken');

async function sendETH() {
  const result = await transferETH(
    "your-private-key-here",
    "******************************************", // recipient
    "0.001" // amount in ETH
  );
  
  if (result.success) {
    console.log(`Transaction successful: ${result.transactionHash}`);
  } else {
    console.error(`Transaction failed: ${result.error}`);
  }
}
```

### 2. ERC-20 Token Transfer

```javascript
const { transferToken } = require('./transferToken');

async function sendToken() {
  const result = await transferToken(
    "your-private-key-here",
    "******************************************", // token contract
    "******************************************", // recipient
    "10" // amount in token units
  );
  
  console.log(result);
}
```

### 3. Check Balances

```javascript
const { createAbstractWallet, getETHBalance, getTokenBalance } = require('./transferToken');

async function checkBalances() {
  const wallet = await createAbstractWallet("your-private-key-here");
  
  // Check ETH balance
  const ethBalance = await getETHBalance(wallet.client, wallet.address);
  console.log(`ETH Balance: ${ethBalance} ETH`);
  
  // Check token balance
  const tokenBalance = await getTokenBalance(
    wallet.client,
    "0xTokenAddress",
    wallet.address
  );
  console.log(`Token Balance: ${tokenBalance.formatted} tokens`);
}
```

## Configuration

### Abstract Blockchain Configuration

```javascript
const ABSTRACT_CONFIG = {
  rpcUrl: "https://api.mainnet.abs.xyz",
  chainId: 11124, // Abstract mainnet
  name: "Abstract",
  nativeCurrency: {
    name: "Ether",
    symbol: "ETH",
    decimals: 18,
  },
};
```

## API Reference

### Core Functions

#### `createAbstractWallet(privateKey)`
Creates a wallet signer for the Abstract blockchain.

**Parameters:**
- `privateKey` (string): Private key with or without 0x prefix

**Returns:**
- Object with `account`, `client`, and `address` properties

#### `transferETH(privateKey, recipientAddress, amount)`
Transfers native ETH to a recipient.

**Parameters:**
- `privateKey` (string): Sender's private key
- `recipientAddress` (string): Recipient's address
- `amount` (string): Amount in ETH (e.g., "0.1")

**Returns:**
- Transaction result object with success status and details

#### `transferToken(privateKey, tokenAddress, recipientAddress, amount)`
Transfers ERC-20 tokens to a recipient.

**Parameters:**
- `privateKey` (string): Sender's private key
- `tokenAddress` (string): Token contract address
- `recipientAddress` (string): Recipient's address
- `amount` (string): Amount in token units (e.g., "100")

**Returns:**
- Transaction result object with success status and details

#### `batchTransfer(privateKey, transfers)`
Executes multiple transfers in sequence.

**Parameters:**
- `privateKey` (string): Sender's private key
- `transfers` (Array): Array of transfer objects

**Transfer Object Format:**
```javascript
{
  to: "0xRecipientAddress",
  amount: "0.1",
  tokenAddress: "0xTokenAddress" // Optional, omit for ETH transfers
}
```

### Utility Functions

#### `validateAddress(address)`
Validates if an address is a valid Ethereum address.

#### `getETHBalance(client, address)`
Gets the ETH balance of an address.

#### `getTokenBalance(client, tokenAddress, walletAddress)`
Gets the token balance with proper decimal formatting.

## Testing

Run the test script to verify functionality:

```bash
node abstract/test-transfer.js
```

**Important:** Update the `TEST_CONFIG` in `test-transfer.js` with your actual private key and addresses before running wallet tests.

## Error Handling

All functions include comprehensive error handling:

```javascript
const result = await transferETH(privateKey, recipient, amount);

if (result.success) {
  console.log(`Success: ${result.transactionHash}`);
  console.log(`Block: ${result.blockNumber}`);
  console.log(`Gas used: ${result.gasUsed}`);
} else {
  console.error(`Failed: ${result.error}`);
}
```

## Security Notes

- ⚠️ **Never commit private keys to version control**
- ⚠️ **Use environment variables for sensitive data**
- ⚠️ **Test with small amounts first**
- ⚠️ **Validate all addresses before transactions**
- ⚠️ **Check balances before transfers**

## Example Environment Setup

Create a `.env` file:

```env
PRIVATE_KEY=your_private_key_here
RECIPIENT_ADDRESS=******************************************
TOKEN_ADDRESS=******************************************
```

Then use in your code:

```javascript
require('dotenv').config();

const privateKey = process.env.PRIVATE_KEY;
const recipient = process.env.RECIPIENT_ADDRESS;
```

## Files

- `transferToken.js` - Main implementation with all transfer functions
- `test-transfer.js` - Test script and usage examples
- `README.md` - This documentation file

## Support

For issues related to the Abstract blockchain, visit:
- [Abstract Documentation](https://docs.abs.xyz/)
- [Abstract Foundation GitHub](https://github.com/Abstract-Foundation)
