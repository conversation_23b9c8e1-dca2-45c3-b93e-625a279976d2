const { createWalletClient, http } = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { abstract } = require("viem/chains");
const crypto = require("crypto");
const { v4 } = require("uuid");
const { io } = require("socket.io-client");
const { sleep } = require("../../helper");
let pRetry = undefined;

const random = (xl) => {
  return crypto.getRandomValues(new Uint8Array(xl));
};
const encodeBase64 = (xl) => {
  let yl = xl;
  typeof yl == "string" && (yl = encoder.encode(yl));
  const wl = 32768,
    _l = [];
  for (let El = 0; El < yl.length; El += wl)
    _l.push(String.fromCharCode.apply(null, yl.subarray(El, El + wl)));
  return btoa(_l.join(""));
};

const generateRandom36 = () => {
  return encodeBase64(random(36))
    .replace(/=/g, "")
    .replace(/\+/g, "-")
    .replace(/\//g, "_");
};

const encodeSHA256 = async (xl) => {
  let yl = new TextEncoder().encode(xl);
  return new Uint8Array(await crypto.subtle.digest("SHA-256", yl));
};
async function s256Generator(xl, yl = "S256") {
  if (yl != "S256") return xl;
  {
    let wl = await encodeSHA256(xl);
    return encodeBase64(wl)
      .replace(/=/g, "")
      .replace(/\+/g, "-")
      .replace(/\//g, "_");
  }
}

const login = async (privateKey) => {
  let codeVerifier = generateRandom36();
  let codeChallenge = await s256Generator(codeVerifier);
  const account = privateKeyToAccount(privateKey);

  let stateCode = generateRandom36();
  let caId = v4();

  await initOAuth(caId, codeChallenge, stateCode);

  // await fetch("https://auth.privy.io/api/v1/oauth/init", {
  //   headers: {
  //     accept: "application/json",
  //     origin: "https://beta.roachracingclub.com",
  //     "accept-language": "en-US,en;q=0.9",
  //     "content-type": "application/json",
  //     "privy-app-id": "cm6m8xag801dt12514xhntb90",
  //     "privy-ca-id": caId,
  //     "privy-client": "react-auth:1.97.0",
  //     Referer: "https://beta.roachracingclub.com/",
  //     "Referrer-Policy": "strict-origin-when-cross-origin",
  //   },
  //   body: JSON.stringify({
  //     provider: "privy:cm04asygd041fmry9zmcyn5o5",
  //     redirect_to: "https://beta.roachracingclub.com/auth",
  //     code_challenge: codeChallenge,
  //     state_code: stateCode,
  //   }),
  //   method: "POST",
  // });

  let nonceResponseJson = await (
    await fetch("https://privy.abs.xyz/api/v1/siwe/init", {
      headers: {
        origin: "https://privy.abs.xyz",
        accept: "application/json",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        "privy-app-id": "cm04asygd041fmry9zmcyn5o5",
        "privy-ca-id": caId,
        "privy-client": "react-auth:2.6.2",
        Referer: "https://privy.abs.xyz/",
      },
      body: JSON.stringify({
        address: account.address,
      }),
      method: "POST",
    })
  ).json();

  const nNonce = nonceResponseJson.nonce;
  const dateIssue = nonceResponseJson.expires_at;
  const message = `privy.abs.xyz wants you to sign in with your Ethereum account:\n${account.address}\n\nBy signing, you are proving you own this wallet and logging in. This does not initiate a transaction or cost any fees.\n\nURI: https://privy.abs.xyz\nVersion: 1\nChain ID: 1\nNonce: ${nNonce}\nIssued At: ${dateIssue}\nResources:\n- https://privy.io`;

  const walletClient = createWalletClient({
    chain: abstract,
    signer: account,
    transport: http(`https://api.mainnet.abs.xyz`),
  });

  const signature = await walletClient.signMessage({
    account,
    message,
  });

  let authenticateResponse = await fetch(
    "https://privy.abs.xyz/api/v1/siwe/authenticate",
    {
      headers: {
        origin: "https://privy.abs.xyz",
        accept: "application/json",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        priority: "u=1, i",
        "privy-app-id": "cm04asygd041fmry9zmcyn5o5",
        "privy-ca-id": caId,
        "privy-client": "react-auth:2.6.2",

        Referer: "https://privy.abs.xyz/",
        "Referrer-Policy": "strict-origin",
      },
      body: JSON.stringify({
        message: message,
        signature: signature,
        chainId: "eip155:1",
        walletClientType: "metamask",
        connectorType: "injected",
        mode: "login-or-sign-up",
      }),
      method: "POST",
    }
  );
  let authenticateResponseJson = await authenticateResponse.json();
  let nextCookies = authenticateResponse.headers
    .getSetCookie()
    .map((it) => it.split(";")[0])
    .join(";");

  let accessToken = authenticateResponseJson.token;

  let response4 = await fetch(
    "https://privy.abs.xyz/api/oauth/authorization_code",
    {
      headers: {
        origin: "https://privy.abs.xyz",
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        "content-type": "application/json",
        priority: "u=1, i",
        "privy-app-id": "cm04asygd041fmry9zmcyn5o5",
        "privy-client": "privy-oauth-provider:1.0.0",
        cookie: nextCookies,
        Referer: "https://privy.abs.xyz/",
        "Referrer-Policy": "strict-origin",
      },
      body: JSON.stringify({
        redirect_to: "https://privy.abs.xyz",
        state: stateCode,
        code_challenge: codeChallenge,
        code_challenge_method: "S256",
        client_id: "cm6m8xag801dt12514xhntb90",
      }),
      method: "POST",
    }
  );

  let response4Json = await response4.json();

  let response5 = await fetch(response4Json.location, {
    headers: {
      origin: "https://privy.abs.xyz",
      accept:
        "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
      "accept-language": "en-US,en;q=0.9",
      priority: "u=0, i",

      Referer: "https://privy.abs.xyz/",
      "Referrer-Policy": "strict-origin",
    },
    body: null,
    redirect: "manual",
    method: "GET",
  });
  let authUrlRaw = response5.headers.get("location");
  let authUrl = new URL(authUrlRaw);
  let authCode = authUrl.searchParams.get("privy_oauth_code");

  let response6 = await fetch(
    "https://auth.privy.io/api/v1/oauth/authenticate",
    {
      headers: {
        origin: "https://beta.roachracingclub.com",
        accept: "application/json",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        priority: "u=1, i",
        "privy-app-id": "cm6m8xag801dt12514xhntb90",
        "privy-ca-id": caId,
        "privy-client": "react-auth:1.97.0",
        Referer: "https://beta.roachracingclub.com/",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: JSON.stringify({
        authorization_code: authCode,
        state_code: stateCode,
        code_verifier: codeVerifier,
        provider: "privy:cm04asygd041fmry9zmcyn5o5",
      }),
      method: "POST",
    }
  );

  let response6Json = await response6.json();

  let response7 = await fetch(
    "https://beta.roachracingclub.com/api/v1/auth/web/sign-in",
    {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        "content-type": "application/json",
        priority: "u=1, i",
        "privy-token": response6Json.token,
        cookie: `token=${response6Json.token}; privy-id-token=${response6Json.identity_token}`,
        Referer: authUrlRaw,
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: JSON.stringify({
        gaTag: "",
        user: response6Json.user,
      }),
      method: "POST",
    }
  );

  return await response7.json();
};

async function initOAuth(caId, codeChallenge, stateCode) {
  pRetry = await import("p-retry").then((mod) => mod.default);
  return await pRetry(
    async () => {
      const response = await fetch("https://auth.privy.io/api/v1/oauth/init", {
        headers: {
          accept: "application/json",
          origin: "https://beta.roachracingclub.com",
          "accept-language": "en-US,en;q=0.9",
          "content-type": "application/json",
          "privy-app-id": "cm6m8xag801dt12514xhntb90",
          "privy-ca-id": caId,
          "privy-client": "react-auth:1.97.0",
          Referer: "https://beta.roachracingclub.com/",
          "Referrer-Policy": "strict-origin-when-cross-origin",
        },
        body: JSON.stringify({
          provider: "privy:cm04asygd041fmry9zmcyn5o5",
          redirect_to: "https://beta.roachracingclub.com/auth",
          code_challenge: codeChallenge,
          state_code: stateCode,
        }),
        method: "POST",
      });

      if (!response.ok) {
        throw new Error(`Fetch failed with status ${response.status}`);
      }

      return response.json();
    },
    {
      retries: 3, // You can adjust the retry count here
      onFailedAttempt: (error) => {
        console.warn(
          `Attempt ${error.attemptNumber} failed. There are ${error.retriesLeft} retries left.`
        );
      },
    }
  );
}

const stopCharge = async (id, accessToken) => {
  return await fetch(
    `https://beta.roachracingclub.com/api/v1/charge/stop/${id}`,
    {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9,vi;q=0.8,ar;q=0.7",
        authorization: `Bearer ${accessToken}`,
        priority: "u=1, i",
        "sec-ch-ua":
          '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        cookie:
          "privy-session=t; AWSALBCORS=rjuptLX4jWkZT7cp+CrqvKany/DLamxJp1pT6Fo0H4rVWUUaTjBmbMN22VnQQfIA+hpq5c3W0B8/PGkFLG3xTfsLNmLYyUlHt2nNxqh/o+Bmzlm2qplu9Ti8cwfj; privy-token=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImVZZ3FCc2c4Q3J1VGV5dm9aeFFseV8wWURkejVVanFNM3pkTVJjSDhfV3MifQ.***************************************************************************************************************************************************************************************************************************.SDWvHmJn_jqPPFJ051EbS3_dU6PywDC6gx7meQTxYNeWt8SHpuCeUc8frgz1g1Fb0Kc9h95MidHMRKoeEhD_8A; privy-id-token=eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6ImVZZ3FCc2c4Q3J1VGV5dm9aeFFseV8wWURkejVVanFNM3pkTVJjSDhfV3MifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QWaa3paV1wv-c_khyUuaZlcyNCmLzDLtHwOTG58veF0EJrUGOM-FoaEZxEqwg9jAs6oTTnOIxAlgu5oKdp6SPQ; AWSALB=jLebwjTgSKmuhtRx4zmVFfGEqHponAv3xPphTPQ3K7kXG2mYmJnbEPgclplGN/fi00znFGD/iF2+1lo3bUOjFdYPQpzjnSLeiEHcj58vYvzoz8DUGQNefIF5ia/4",
        Referer: "https://beta.roachracingclub.com/home",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: null,
      method: "POST",
    }
  )
    .then(async function (response) {
      console.log("stop charge successfully", await response.json());
    })
    .catch((error) => {
      console.log("stop charge failed: " + error);
    });
};

const getSlots = async (accessToken) => {
  let slotResponse = await fetch(
    "https://beta.roachracingclub.com/api/v1/slots?pageSize=100",
    {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        Referer: "https://beta.roachracingclub.com/home",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "user-agent":
          "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
      },
      method: "GET",
    }
  );
  let slotResponseJson = await slotResponse.json();
  if (slotResponseJson?.result?.length == 0) {
    console.log("Buy new Gian");
    await fetch("https://beta.roachracingclub.com/api/v1/slots/buy", {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        "content-type": "application/json",
        priority: "u=1, i",
        Referer: "https://beta.roachracingclub.com/home",
      },
      body: null,
      method: "POST",
    });
    await fetch("https://beta.roachracingclub.com/api/v1/roaches/buy", {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        "content-type": "application/json",
        priority: "u=1, i",
        Referer: "https://beta.roachracingclub.com/home",
      },
      body: JSON.stringify({ slotIndex: 0 }),
      method: "POST",
    });

    return getSlots(accessToken);
  }

  return slotResponseJson;
};

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

const getTasks = async (accessToken) => {
  let taskResponse = await fetch(
    "https://beta.roachracingclub.com/api/v1/tasks",
    {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        Referer: "https://beta.roachracingclub.com/home",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "user-agent":
          "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36",
      },
      method: "GET",
    }
  );
  let taskResponseJson = await taskResponse.json();
  let claimableTask = taskResponseJson.dailyReward.rewards.find(
    (it) => it.status == "can_claim"
  );

  if (claimableTask) {
    console.log("Have task, claim");
    await fetch("https://beta.roachracingclub.com/api/v1/tasks/claim", {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        "content-type": "application/json",
        Referer: "https://beta.roachracingclub.com/home",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: JSON.stringify({ taskId: claimableTask.id }),
      method: "POST",
    });
  }

  return taskResponseJson;
};

const playGame = async (accessToken, playerId) => {
  let response = await fetch(
    "https://beta.roachracingclub.com/api/v1/race/register",
    {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        "content-type": "application/json",
        priority: "u=1, i",
        Referer: "https://beta.roachracingclub.com/home",
      },
      body: JSON.stringify({ roachId: playerId, templateId: "" }),
      method: "POST",
    }
  );

  let responseJson = await response.json();
  console.log(responseJson, "responseJson");
  let wsUrl = responseJson.raceBackUrl;
  let gameUUID = responseJson.gameUuid;
  let token = responseJson.token;
  playGameSocket(accessToken, wsUrl, gameUUID, playerId, token);

  return response;
};

const playGameSocket = (accessToken, wsUrl, gameUUID, playerId, token) => {
  const socket = io(wsUrl);
  let boostActive = false;
  let intervalBoost;

  socket.on("connect", () => {
    socket.emit("joingame", {
      game: gameUUID,
      user: playerId,
      token: token,
    });
    intervalBoost = setInterval(async () => {
      if (boostActive) return;
      boostActive = true;
      socket.emit("openBoost", {
        direction: Math.random() < 0.5 ? "UP" : "DOWN",
      });
      await delay(5000);
      socket.emit("closeBoost");
      boostActive = false;
    }, 5000);
  });

  socket.on("kick", (args) => {
    if (args.playerId == playerId) {
      socket.close();
      socket.disconnect();
      clearInterval(intervalBoost);
      chargeIfNeed(playerId, accessToken);
    }
  });

  socket.onAny((eventName, ...args) => {
    console.log(eventName, args);
  });
};

const chargeIfNeed = (playerId, accessToken) => {
  return fetch(
    `https://beta.roachracingclub.com/api/v1/charge/start/${playerId}`,
    {
      headers: {
        accept: "application/json, text/plain, */*",
        "accept-language": "en-US,en;q=0.9",
        authorization: `Bearer ${accessToken}`,
        Referer: "https://beta.roachracingclub.com/home",
        "Referrer-Policy": "strict-origin-when-cross-origin",
      },
      body: null,
      method: "POST",
    }
  )
    .then(async function (response) {
      console.log(await response.json());
      console.log("charge success");
    })
    .catch(function (err) {
      console.log("charge error", err);
    });
};

const main = async () => {
  let privateKeys = [
    "0xc8779801273cb9e0a2501d863ee5ea5cbf89609cab9686224277b77426b30fc5",
    // "0x6691ab469fef4762f0347666ceaaee48f9e67225c6042ae3c2278d5a09fc700e",
    // "0xa9334cb60b03689778a46dcb549391e49e9bb25459b69113f5b53b800d11f1dc",
    // "0x242faeeeab6ae60bc6059a1f934afb413844b9bb6ec890c9b2046bfac0d496f8",
    // "0x804401dd3735c49652d85bedcc6be43265ca7dd4bc0520bbf859660a3de681b4",
  ];

  const privatekeys = [
    // {
    //   key: "0xc8779801273cb9e0a2501d863ee5ea5cbf89609cab9686224277b77426b30fc5",
    //   token:
    //     "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.QDA8FSJjJ5qGinGQm2jg1Dkd-epPktV2C4i-hEp5og4",
    // },
    {
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjB4MjNDNTcwYzc1MkM0NTZEMGI1RjM5NmIwZDJEZDI0QTFFNzczMDA4RCIsImlhdCI6MTc0NTQyMDQ1NSwiZXhwIjoxNzQ1NTA2ODU1fQ.d-VkrOKzskJXBdXpik4Ood4uLwtZ6GDyQ4DFOmfftAM",
    },
    {
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.ZYZKE4mJLrsAUKbRNEG-9u_7S3ARhxDcNOgD-mfp6UE",
    },
    {
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.o8Q6ib0vLZXEtSIB4dGQeqSuz38DkF5w-mXLCwzEvSM",
    },
    {
      token:
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************.2Qwdp_ElYLfQPsob2FcjgwVtolPFRq9RP-EwMbB6uyk",
    },
  ];
  for (const account of privateKeys) {
    try {
      const data = await login(account);
      const accessToken = data?.initData?.token;
      // const accessToken = account.token;

      let slots = await getSlots(accessToken);

      for (const slot of slots.result) {
        console.log(slot, "slot");
        if (!slot.roach) {
          console.log("continue");
          continue;
        }

        if (
          slot?.roach?.currentEnergy == 0 &&
          slot.roach.status == "available"
        ) {
          await chargeIfNeed(slot.roach.id, accessToken);
          continue;
        }

        if (
          slot?.roach?.chargeSession?.chargedEnergy == slot?.roach?.maxEnergy
        ) {
          await stopCharge(slot.roach.id, accessToken);
          slot.roach.status = "available";
        }

        await getTasks(accessToken);
        if (slot.roach.status == "available") {
          await playGame(accessToken, slot.roach.id);
          // await sleep(1000 * 60 * 2);
        }
        if (slot.roach.status == "in_race") {
          playGameSocket(
            accessToken,
            loginInfo.initData.user.currentRace.raceBackUrl,
            loginInfo.initData.user.currentRace.gameUuid,
            slot.roach.id,
            loginInfo.initData.user.currentRace.token
          );

          // await sleep(1000 * 60 * 2);
        }
      }
    } catch (error) {
      console.log(`private key error`, error);
    }
  }
};

main();
const intervalBalance = setInterval(main, 1.75 * 60 * 60 * 1000);
// console.log("Monitoring balance changes...");
