const {
  createAbstractWallet,
  validateAddress,
  getETHBalance,
  getTokenBalance,
  transferETH,
  transferToken,
  batchTransfer,
} = require('./transferToken');

/**
 * Test script demonstrating the Abstract token transfer functionality
 */
async function runTests() {
  console.log("🧪 Abstract Token Transfer Tests");
  console.log("================================\n");

  // Test configuration - REPLACE WITH YOUR OWN VALUES
  const TEST_CONFIG = {
    // ⚠️  IMPORTANT: Replace with your actual private key
    privateKey: "YOUR_PRIVATE_KEY_HERE",
    
    // Replace with actual recipient address
    recipientAddress: "******************************************",
    
    // Replace with actual token addresses on Abstract
    tokenAddress: "******************************************",
  };

  try {
    // Test 1: Address validation
    console.log("📋 Test 1: Address Validation");
    console.log(`Valid recipient: ${validateAddress(TEST_CONFIG.recipientAddress)}`);
    console.log(`Valid token: ${validateAddress(TEST_CONFIG.tokenAddress)}`);
    console.log(`Invalid address: ${validateAddress("0xinvalid")}\n`);

    // Test 2: Create wallet and check balances
    console.log("📋 Test 2: Wallet Creation and Balance Check");
    
    if (TEST_CONFIG.privateKey === "YOUR_PRIVATE_KEY_HERE") {
      console.log("⚠️  Please update TEST_CONFIG with your actual private key to run wallet tests\n");
      return;
    }

    const wallet = await createAbstractWallet(TEST_CONFIG.privateKey);
    console.log(`Wallet Address: ${wallet.address}`);

    // Check ETH balance
    const ethBalance = await getETHBalance(wallet.client, wallet.address);
    console.log(`ETH Balance: ${ethBalance} ETH`);

    // Check token balance
    try {
      const tokenBalance = await getTokenBalance(
        wallet.client,
        TEST_CONFIG.tokenAddress,
        wallet.address
      );
      console.log(`Token Balance: ${tokenBalance.formatted} tokens`);
    } catch (error) {
      console.log(`Token balance check failed: ${error.message}`);
    }
    console.log();

    // Test 3: Small ETH transfer (uncomment to test actual transfer)
    /*
    console.log("📋 Test 3: ETH Transfer");
    const ethResult = await transferETH(
      TEST_CONFIG.privateKey,
      TEST_CONFIG.recipientAddress,
      "0.001" // 0.001 ETH
    );
    console.log("ETH Transfer Result:", ethResult);
    console.log();
    */

    // Test 4: Token transfer (uncomment to test actual transfer)
    /*
    console.log("📋 Test 4: Token Transfer");
    const tokenResult = await transferToken(
      TEST_CONFIG.privateKey,
      TEST_CONFIG.tokenAddress,
      TEST_CONFIG.recipientAddress,
      "1" // 1 token
    );
    console.log("Token Transfer Result:", tokenResult);
    console.log();
    */

    // Test 5: Batch transfer (uncomment to test actual transfers)
    /*
    console.log("📋 Test 5: Batch Transfer");
    const batchTransfers = [
      {
        to: TEST_CONFIG.recipientAddress,
        amount: "0.001", // ETH transfer
      },
      {
        to: TEST_CONFIG.recipientAddress,
        amount: "1",
        tokenAddress: TEST_CONFIG.tokenAddress, // Token transfer
      },
    ];

    const batchResults = await batchTransfer(TEST_CONFIG.privateKey, batchTransfers);
    console.log("Batch Transfer Results:", batchResults);
    */

    console.log("✅ All tests completed successfully!");

  } catch (error) {
    console.error("❌ Test failed:", error.message);
    console.error("Stack trace:", error.stack);
  }
}

/**
 * Example of how to use the functions in your own code
 */
async function usageExample() {
  console.log("\n📖 Usage Examples");
  console.log("=================\n");

  console.log("// 1. Basic ETH Transfer");
  console.log(`const result = await transferETH(
  "your-private-key",
  "******************************************",
  "0.1" // 0.1 ETH
);
console.log(result.transactionHash);`);

  console.log("\n// 2. ERC-20 Token Transfer");
  console.log(`const result = await transferToken(
  "your-private-key",
  "0xTokenAddress",
  "******************************************",
  "100" // 100 tokens
);
console.log(result.transactionHash);`);

  console.log("\n// 3. Check Balance Before Transfer");
  console.log(`const wallet = await createAbstractWallet("your-private-key");
const balance = await getETHBalance(wallet.client, wallet.address);
console.log(\`Balance: \${balance} ETH\`);`);

  console.log("\n// 4. Batch Transfer");
  console.log(`const transfers = [
  { to: "0xRecipient1", amount: "0.1" }, // ETH
  { to: "0xRecipient2", amount: "50", tokenAddress: "0xToken" }, // Token
];
const results = await batchTransfer("your-private-key", transfers);`);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests()
    .then(() => usageExample())
    .catch(console.error);
}

module.exports = {
  runTests,
  usageExample,
};
