const data = require('./data.json');
const moment = require('moment');
const {chain, filter, map, reduce} = require('lodash')
  const fs = require('fs');

const groupByDay = (data) => {
  let data1 = {};
  
  for (const item of data){
    const key = item.AirItinerary.OriginDestinationOptions.OriginDestinationOption.FlightSegment.DepartureDateTime;

    if (data1[moment(key).format('MM/DD/YYYY')]) {
      data1[moment(key).format('MM/DD/YYYY')].push(item);
      continue; 
    }

    data1[moment(key).format('MM/DD/YYYY')] = [item];
  }
  
  return data1;
}

const orderByKey = (array, key, sortBy) => {
  if (!array?.length) {
    return;
  }

  let swapped;
  const compareFunctionName = sortBy === 'desc' ? 'isLessThan' : 'isGreaterThan';

  do {
    swapped = false;

    for (let i = 0; i < array.length - 1; i++) {
      if (BigNumberInstance(array[i][key])[compareFunctionName](array[i + 1][key])) {
        let temp = array[i];
        array[i] = array[i + 1];
        array[i + 1] = temp;
        swapped = true;
      }
    }
  } while (swapped);

  return array;
};

const format = () => {
  const _data = groupByDay(data.PricedItineraries.PricedItinerary);
  // const newData = {};
  
  // const data = chain(_data).filter(item => {
  //   return item.
  // })
  
  const data1 = filter(_data, item => {
    return !item.AirItineraryPricingInfo?.ItinTotalFare?.TotalFare?.amount;
  })

  // Object.keys(_data).map(item => {
  //   newData[item] = _data[item].reduce((min, trip) => {
  //     console.log(trip, "trip")
  //     return trip.AirItineraryPricingInfo.ItinTotalFare.TotalFare.amount < min?.AirItineraryPricingInfo?.ItinTotalFare?.TotalFare?.amount ? trip : min;
  //   })
  // })
  
  fs.writeFile("./datatest", JSON.stringify(data1), function(err) {
    if(err) {
        return console.log(err);
    }
    console.log("The file was saved!");
}); 
}

format();

const log = moment(date.replace('Z', timeZone.replace('GMT', ''))).utcOffset('Z').format();

console.log(log, "log")