const { ethers } = require("ethers");

// Connect to the Fantom network via a public RPC URL
const provider = new ethers.providers.JsonRpcProvider("https://rpcapi.fantom.network");


// List of addresses you want to check the balance for
const addresses = [
  "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "0x177Aef6a281028Cb71569DbEBCBc3E086cB51Bb0",
    "0xc82d6571e45581F1EdA4ef44772BAFB6A536a85c",
    "0x0f741A09A86CD0b0Bb7e3eF4E17988F292F750EE",
    "0xF71A3469919C4Ec91b9fbDDa3f3d2C9fa5720426",
    "0x4Fa746816C893F598afdadBb29d179Fe1007420a",
    "0x89cD95bEFDfe8D55A1E3962aA2F4c729DF33f959",
    "0x523A5bc845584F34d2698807A5522a6114c05821",
    "0x24C8cb02DAdEcd6F4dE8544968296Db737232d0e",
    "0x29de20141ad05D166B88E95402741295e29A011C",
    "0x0D5ccf3A4F9cBb584b138405Dea5478aab4F63E1",
    "0x3B1e1011B3c4AD9B9FAa912b90C2c5422708B3bb",
    "0x04DD7bB278Ec9684875F4d3110120f8B98F1b0a2",
    "0x7dAE20FFbD9eDD368DFA72A6ECaeAe4DD3d31A72",
    "0xAC935068AA9b982b1db86000b8f6379e28279B3D",
    "0x7D895e588232Dac1B978ec96A127a5b315F2Ff4b",
    "0x49A5C0bb35f442135EDc3dA5f31e5718223055D9",
    "0x2F2c74864a60a8ad4fC9661f8184E4061Eda39D4",
    "0x52947CF8e3ad20F14F8621718b40f0abDC26f62B",
    "0xF8732603961002bD7352Fe0F0251888A91773407",
    "0xE7B57F5989247EC6180AC80e5bEB5C4f7b546Ab8",
    "0xB739807dc480828D6deb97911036395396dB6533",
    "0xEc670c56388d3E8800491f9f6f635FAA9CfFcDaB",
    "0x198c56b9De6dcdAd036722BbC044678651ef5700",
    "0x624D8D6b760980Bf242f3250D043DA31d2300726",
    "0xbcCF5d6c3dAFF31a5F9F9d800094609998D27152",
    "0x0BECf8EE731B3D90b416C0ba8Ca8149E0E0D330C",
    "0x4a80d9FbCce7F4DF0720441c04aa7A50C3A8878A",
    "0xecCcE7d7059DC14fF6E3c3fcfE21067b57087814",
    "0xF7458bbFf4C8029393c17738d272656d77ff41a6",
    "0xfD7314f3021cD9484fede5bDFa0F2C10c056E109",
    "0x3A4fB4C23B4e153c0a4fE6B7821d981E0794859a",
    "0xAa923993dA197ba3b7bAeFe11020fC6782ae5a29",
    "0x2df0FB584620B097852c9cbbfaf656B0e95E6181",
    "0x0b20F77336F731b26091362F544424240a8E1B3d",
    "0xDC5A02c2e3000b48F670AC4C6Cd8b1740D18fDD2",
    "0xDc6727AF47BebC0a268D9545FB4D7Ba416fb431C",
    "0xa210d05eB408C22860ba46BeD95081b85651c7Ec",
    "0x47f3E711284512782AB168974433dB8ffa5f7e15",
    "0xFdC24c18833932e38b309141b190C894CdFA1247",
    "0xbB66A8Ddd94ba86549E823df46Da17d6948c0273",
    "0x4428C0447A35Fe2eB0EbbE5A8af61091A779C5d3",
    "0xB9A1D0AB7436B162AF617bc1FC96dF17c68d11AE",
    "0x532d1e849994E3529e31C3453BE4706aE2898240",
    "0xF8D5311998cCe1c1628f67f9891C5D593FEA708A",
    "0x39C2CF0194C2B8669d3A53c9bB4cfaF2bEde06f1",
    "0xD51928B33937c5777241754Aa0A353015a5aa3f0",
    "0xC18b1263f70d7419bC19020eB0337D83dD114883",
    "0xE87107e58cdfD78006281794e17d8b756AEaf447",
    "0x5802e60b6937eE47D0C3c20CC69e849E4b72858F",
    "0x88988C91e91E9B64622DCD2A9633567C8D93291C",
    "0x4e8039EF22b3400de01D7F648272325093A5eEdF",
    "0xB7B9BDeEB7E21d0738F095A1B7E7de00a0B9BEbc",
    "0xCFDDd72C8D420692C724f8FEA9bE91A46A1d5798",
    "0x1F7593055DE5606065AD058e14EfCc93cbB1176F",
    "0x2ACa91434d40833E781995cc1C4c21d6e28AfF10",
    "0xAA70C51A760F827F06800881866b3180d26a4D96",
    "0x7cC08d00074EF4AB0872BE8e45b907E486259402",
    "0xF7AB9E6FCc542Ac2484de9d789e27F159E719262",
    "0x878578c2201267CBa373e46919ca162B96d117a7",
    "0x44577E33635FC4303166e48aa24A4d0f2BC0752A",
    "0x7B1cF8164DadE1dD0753D2D3c2af01Fe2D2e381C",
    "0x2544323D4F2a1208DB21dFF3a70155852291079B",
    "0x58CF155C1dac1A4E9C349B5429c9C93B62B19E42",
    "0x56DEDb007a652B3fF7d5b2dA4057D49D308d1DE9",
    "0x24052c0Ce39932f0d90926177D09029ef6A47361",
    "0x800A0CaE9dD40F1C8f9314545D4E57Cf46D4B084",
    "0xe9bb1BCD480e960Ee06F2346dD1490aF9Bc9041F",
    "0xb549B69d8c868EE3167feB52Ea6f3F4a9249076B",
    "0x7739794F05F7ba4549e0FccD2ca122B2C7Ec41E0",
    "0xA5Aa77BE804be85806d3D2eb30F5eEF9302A7bD4",
    "0xD01b00C3EC35Ac4e3eF67bfBf50Cb03Ac217674f",
    "0x6784F875ef2e0FA75f071F83Ae25696f3396D64B",
    "0xAF011c073C9e126e01b346863624Cb6F5842F58d",
    "0x85e478706ff336ca53cA13551A3Dd86757e414A5",
    "0x245686355C0E93551d221ce2093eA180D0BEbfdD",
    "0x684D917E60d313E83CeDd9A1d502F013909d8321",
    "0xB7377012d84f6E16F81682F96fdE9e9f41e5892B",
    "0xF8c4362AE7b98ee81f2e5761Ece6a3EC5fdAd63a",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************",
    "******************************************"]
;

// Function to fetch balances of multiple addresses
async function getBalances(addresses) {
  try {
    const balancePromises = addresses.map(async (address) => {
      const balance = await provider.getBalance(address);
      return { address, balance: ethers.utils.formatEther(balance) };
    });

    const balances = await Promise.all(balancePromises);
    return balances;
  } catch (error) {
    console.error('Error fetching balances:', error);
  }
}

// Fetch and print balances
getBalances(addresses).then(balances => {
  console.log('Balances:', balances);
});