const kafka = require('kafka-node');

const client = new kafka.KafkaClient({ kafkaHost: 'localhost:9092' });
const consumer = new kafka.Consumer(client, [{ topic: 'job-topic' }]);

consumer.on('message', (message) => {
  console.log('Received job:', JSON.parse(message.value));
  // Process the job here

  // Commit the offset to mark the job as consumed
  consumer.commit((err, data) => {
    if (err) {
      console.error('Error committing offset:', err);
    } else {
      console.log('Offset committed successfully:', data);
    }
  });
});

consumer.on('error', (err) => {
  console.error('Consumer error:', err);
});