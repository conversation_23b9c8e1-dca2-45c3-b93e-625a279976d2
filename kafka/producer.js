const kafka = require('kafka-node');

const client = new kafka.KafkaClient({ kafkaHost: 'localhost:9092' });
const producer = new kafka.Producer(client);
console.log(producer, "producer")

producer.on('ready', () => {
  console.log('Producer is ready');

  // Produce a job to the Kafka topic
  const job = { id: 1, name: 'Example Job' };
  const payload = [{ topic: 'job-topic', messages: JSON.stringify(job) }];
  producer.send(payload, (err, data) => {
    if (err) {
      console.error('Error producing job:', err);
    } else {
      console.log('Job produced successfully:', data);
    }

    producer.close();
  });
});

producer.on('error', (err) => {
  console.error('Producer error:', err);
});