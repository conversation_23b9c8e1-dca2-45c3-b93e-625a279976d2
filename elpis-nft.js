const axios = require("axios").default;

const heroesQuery = `
  query UserHeroesByTokenIds($tokenIds: [Float!]!, $name: String, $sortDirection: SortDirection, $sortField: String, $page: Int, $size: Int) {
    userHeroesByTokenIds(tokenIds: $tokenIds, name: $name, sortDirection: $sortDirection, sortField: $sortField, page: $page, size: $size) {
      items {
        _id
        owner {
          guild
          address
          _id
        }
        type
        rarity
        gender
        name
        description
        image
        tokenId
        heroRace
        maxLevel
        strength
        vitality
        agility
        ownedMythical
        bodyParts {
          percentage
          isMythical
          name
          element
          type
        }
        normalSkill {
          damage
          mana
          race
          rarity
          type
          image
          description
          name
        }
        passiveOneSkill {
          damage
          mana
          race
          rarity
          type
          image
          description
          name
        }
        passiveTwoSkill {
          damage
          mana
          race
          rarity
          type
          image
          description
          name
        }
        ultimateOneSkill {
          damage
          mana
          race
          rarity
          type
          image
          description
          name
        }
        ultimateTwoSkill {
          damage
          mana
          race
          rarity
          type
          image
          description
          name
        }
        isGenesis
        recruitedCount
        summonTimes
        recruitmentSignature
        star
        latestTransfer
        level
        exp
      }
      total
      size
      page
    }
  }`;

const equipmentQuery = `
  query Query($tokenIds: [Float!], $address: String!) {
    getNFTEquipments(tokenIds: $tokenIds, address: $address) {
      items {
        _id
        owner {
          address
        }
        name
        description
        tokenId
        image
        type
        rarity
        race
        attribute1
        attribute2
        subAttribute1
        subAttribute2
        subAttribute3
        subAttribute4
        set
        workshopCount
        star
        latestTransfer
        level
      }
    }
  }
`;

async function getHeroes() {
  return axios.default({
    method: "post",
    url: `https://dev.elpis.game/elpis-be/graphql`,
    headers: {
      "content-type": "application/json",
      "x-blockchain-id": "bsc",
    },
    data: JSON.stringify({
      query: heroesQuery,
      variables: {
        tokenIds: [1, 2, 3],
      },
    }),
  });
}

async function getEquipments() {
  return axios.default({
    method: "post",
    url: `https://dev.elpis.game/elpis-be/graphql`,
    headers: {
      "content-type": "application/json",
    },
    data: JSON.stringify({
      query: equipmentQuery,
      variables: {
        address: "******************************************",
        tokenIds: [1, 2, 3],
      },
    }),
  });
}

async function getElpisNFT() {
  try {
    const [heroes, equipments] = await Promise.all([
      getHeroes(),
      getEquipments(),
    ]);
    console.log("======= heroes ========");
    console.log(heroes.data?.data?.userHeroesByTokenIds);
    console.log("======= equipment ========");
    console.log(equipments.data?.data?.getNFTEquipments);
  } catch (err) {
    console.log(err.response.data.errors);
  }
}

getElpisNFT();
