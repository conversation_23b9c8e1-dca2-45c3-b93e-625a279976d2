const crypto = require('crypto');
const axios = require('axios').default;

const nonce = new Date().valueOf();
// const secretKey = '0c5110fd7a61b21cea1b6ccfe990acda6bb9b83f4c26b2dceb722383e85a063d';
// const params = {
//     action: "WITHDRAW"
// }
// const payload = `/api/v3.2/availableCurrencies${nonce}${JSON.stringify(params)}`;
// console.log(payload, "signature")

// const hmac = crypto.createHmac('sha384', secretKey);
// hmac.update(payload);

// const signature = hmac.digest('hex');

// const main = async () => {
//     try {
//         const res = await axios.get('https://api.btse.com/spot/api/v3.2/availableCurrencies', {
//             headers: {
//                 'btse-api': '04d285a4987ea94d1aae19274531a1d5ca0f24b20c505e021d45934a7de7fe81',
//                 'btse-nonce': nonce,
//                 'btse-sign': signature
//             },
//             params,
//         })
//         console.log(res, "main")
//     } catch (e) {
//         console.log(e.response.data, "error")
//     }

// }
// main();

let body = {
    "postOnly": false,
    "price": 8500.0,
    "reduceOnly": false,
    "side": "BUY",
    "size": 1,
    "stopPrice": 0.0,
    "symbol": "BTCPFC",
    "time_in_force": "GTC",
    "trailValue": 0.0,
    "triggerPrice": 0.0,
    "txType": "LIMIT",
    "type": "LIMIT"
}

const secretKey = '848db84ac252b6726e5f6e7a711d9c96d9fd77d020151b45839a5b59c37203bx';
const payload = `/api/v2.1/order1624985375123{"postOnly":false,"price":8500.0,"reduceOnly":false,"side":"BUY","size":1,"stopPrice":0.0,"symbol":"BTCPFC","time_in_force":"GTC","trailValue":0.0,"triggerPrice":0.0,"txType":"LIMIT","type":"LIMIT"}`;
console.log(payload, "payload")
const hmac = crypto.createHmac('sha384', secretKey);
hmac.update(payload);

const signature = hmac.digest('hex');
console.log(signature, "signature") 