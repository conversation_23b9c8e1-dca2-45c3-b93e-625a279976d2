const { http, createPublicClient } = require("viem");
const { monadTestnet } = require("viem/chains");
const { default: BigNumber } = require("bignumber.js");
const TelegramBot = require("node-telegram-bot-api");
const { generatePrivateKey, privateKeyToAccount } = require("viem/accounts");
const crypto = require("crypto");
const { HttpsProxyAgent } = require("https-proxy-agent");
const fs = require("fs");
const taskIds = [];
const accountDone = [];

const TELEGRAM_BOT_TOKEN = "**********************************************";
const TELEGRAM_CHAT_ID = -**********;

const publicClient = createPublicClient({
  chain: monadTestnet,
  transport: http(),
});
const bot = new TelegramBot(TELEGRAM_BOT_TOKEN, { polling: true });

let lastBalance = null;

const getBalance = async () => {
  try {
    const balance = await publicClient.getBalance({
      address: "******************************************",
    });
    console.log(balance, "balance");
    return balance;
  } catch (error) {
    console.error("Error fetching balance:", error);
    return null;
  }
};

const monitorBalance = async () => {
  const newBalance = await getBalance();

  if (
    newBalance !== null &&
    lastBalance !== null &&
    newBalance !== lastBalance
  ) {
    console.log(newBalance, "newBalance");
    console.log(lastBalance, "lastBalance");
    const balanceChange = BigNumber(newBalance)
      .minus(lastBalance)
      .div(1e18)
      .toFixed();
    const message = `Change: ${balanceChange} MON`;
    console.log(message, "message");

    getFaucet();
    await bot.sendMessage(TELEGRAM_CHAT_ID, `starting get faucet`);
    clearInterval(intervalBalance);
  }

  lastBalance = newBalance;
};

const generateCaptchaTask = async () => {
  const requestOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      clientKey: "48a86a60b0d6fcbec352f7081868ec04",
      task: {
        type: "TurnstileTaskProxyless",
        websiteURL: "https://testnet.monad.xyz/",
        websiteKey: "0x4AAAAAAA-3X4Nd7hf3mNGx",
      },
    }),
  };

  let res = await (
    await fetch("https://api.2captcha.com/createTask", requestOptions)
  ).json();
  taskIds.push(res.taskId);
};

const getResolveTask = async (taskId) => {
  const requestOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      clientKey: "48a86a60b0d6fcbec352f7081868ec04",
      taskId: taskId,
    }),
  };

  let res = { status: "processing" };
  while (res.status != "ready") {
    res = await (
      await fetch("https://api.2captcha.com/getTaskResult", requestOptions)
    ).json();
  }

  return res;
};

const prepaidCaptcha = async (number) => {
  let promiseAll = [];
  for (let i = 0; i < number; i++) {
    promiseAll.push(generateCaptchaTask());
  }

  await Promise.all(promiseAll);
};

const faucetGet = async (accountAddress, token) => {
  const md5Random = crypto
    .createHash("md5")
    .update(accountAddress)
    .digest("hex");

  const myHeaders = new Headers();
  myHeaders.append("accept", "*/*");
  myHeaders.append("accept-language", "en-US,en;q=0.9");
  myHeaders.append("cache-control", "no-cache");
  myHeaders.append("content-type", "application/json");
  myHeaders.append("origin", "https://testnet.monad.xyz");
  myHeaders.append("pragma", "no-cache");
  myHeaders.append("priority", "u=1, i");
  myHeaders.append("referer", "https://testnet.monad.xyz/");
  myHeaders.append(
    "sec-ch-ua",
    '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"'
  );
  myHeaders.append("sec-fetch-dest", "empty");
  myHeaders.append("sec-fetch-mode", "cors");
  myHeaders.append("sec-fetch-site", "same-origin");
  myHeaders.append(
    "user-agent",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  );

  const raw = JSON.stringify({
    address: accountAddress,
    visitorId: md5Random,
    cloudFlareResponseToken: token,
  });

  const agent = new HttpsProxyAgent(
    `http://********-zone-custom-region-VN:<EMAIL>:6200`,
    {
      signal: AbortSignal.timeout(5000),
    }
  );

  const requestOptions = {
    method: "POST",
    headers: myHeaders,
    body: raw,
    agent: agent,
    signal: AbortSignal.timeout(5000),
  };

  fetch("https://testnet.monad.xyz/api/claim", requestOptions)
    .then((response) => response.text())
    .then((result) => console.log(accountAddress, result))
    .catch((error) => console.error(`claim error of ${accountAddress}`, error));
};

const processAccount = async () => {
  try {
    let taskId = taskIds.shift();
    let respCaptcha = await getResolveTask(taskId);
    let accountPriv = generatePrivateKey();
    let account = privateKeyToAccount(accountPriv);
    console.log(`Process pk ${accountPriv}`);
    console.log(`Process account ${account.address}`);
    faucetGet(account.address, respCaptcha.solution.token);
    accountDone.push({
      privateKey: accountPriv,
      account: account.address,
    });
  } catch (error) {
    console.log(error, "process acc error");
  }
};

const getFaucet = async () => {
  const totalAccount = 100;
  await prepaidCaptcha(totalAccount);
  console.log("Prepare captcha done");

  let i = 0;
  let promiseAll = [];
  while (i < totalAccount) {
    if (promiseAll.length == 1) {
      await Promise.all(promiseAll);
      promiseAll = [];
    }
    promiseAll.push(processAccount());
    i++;
  }
  await Promise.all(promiseAll);

  fs.writeFile("./accounts", JSON.stringify(accountDone), function (err) {
    if (err) {
      return console.log(err);
    }
    console.log("The file was saved!");
  });
};

monitorBalance();
// Run balance check every 10 seconds
const intervalBalance = setInterval(monitorBalance, 30 * 1000);
console.log("Monitoring balance changes...");
