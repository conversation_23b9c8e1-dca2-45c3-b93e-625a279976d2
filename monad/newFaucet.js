const { generatePrivateKey, privateKeyToAccount } = require("viem/accounts");
const crypto = require("crypto");
const { HttpsProxyAgent } = require("https-proxy-agent");
const { default: BigNumber } = require("bignumber.js");
const fs = require("fs");
const { createPublicClient, http } = require("viem");
const { monadTestnet } = require("viem/chains");
const fetch = require("node-fetch");
const TelegramBot = require("node-telegram-bot-api");

const accountDone = [];
const TELEGRAM_BOT_TOKEN = "**********************************************";
const TELEGRAM_CHAT_ID = -**********;

const bot = new TelegramBot(TELEGRAM_BOT_TOKEN, { polling: true });

const pks = [
  "0x742e3c315bf65a212bfaf25e9e5558433e4fae37af997ec5b33763851a04f077",
  "0x61e92495cf009aebb8e0c9ff483d64b6385a4d3686194c32dd4778e1170ecdc8",
  "0x98aec623af386982ba3ab4906d5881462d8517a800621d2b4d2728a0ea11e10e",
  "0x76f88ccb1bdc2255a349e8e945637ef5fcd5e8b41e5b435bc2a4f908758180fc",
  "0xf0172e51cfedc94f02d7bbef6a40f367580374c660eb6d3ea6b4a4338c729db8",
  "0x1abfb6156305133ea7ac70f6d087c2edc7213ec7e6a27a22365bdb8164f7114f",
  "0x264269379a7ce9c2bd3e3bebd4da55ff61fa1e0148e7945dc18bd8e3290efa69",
  "0x7b1958f62303cfef50e4f7eae46ec96af39e899c388679e57b21c64a78ee6456",
  "0xad522aa2fb1fa7bf27089462425e4a08b54072e63953e6949db41816ec05cefb",
  "0x23402fc1068752625e5db3d1bf8504d71b7148dcf5ab336bcf27a0342dca819b",
  "0x132c3f4998271d40e5c8448045c260dffb155dfc823ba90e4193aa36b17f0434",
  "0x88c85ec15f1e8cdfb5ed2a2d8b766f78724f537a077c846eaa839bbfcaa863e3",
  "0xb5a25859832b28f831f67505bc87de5b6cb05d71ba677904c2685dd76ee8a35e",
  "0x7e9a27a64a34abbc3dc408f61e7ca77433b82abe3eefeab61744f9262118c4e3",
  "0x553bcb59ba506887e040dfb612bb7572cf3d314a915b41071fe0e9b9b53dc7c1",
  "0xd33f96a76245b08676854b765c7840da59037975e55c164508f49778e4e865f6",
  "0x588ef523569abb7094d0f0efbe5a272b10a84c75792cf2a6c7cffd4dda01299b",
  "0x9be6209eb76a91cc3c5b3a3ca42b109e9115959542d2508f1c193761a14c575c",
  "0xc437fd83f8540e843811d394ced3eb6c9ea3edbdd0ede3d05a3614cd73f363a7",
  "0xea73922a0d942ca5a7dc3054d44bab8c44189d2159b83865dfbb06b2a21373d7",
  "0x5f8485106bb7e9bf87b0d391f0ed301fdddb8a6ec36c7c3adc6698203346dfc4",
];
const publicClient = createPublicClient({
  chain: monadTestnet,
  transport: http(),
});
let lastBalance = null;

function shuffleArray(array) {
  let shuffled = array.slice();
  for (let i = shuffled.length - 1; i > 0; i--) {
    let j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

const faucetGet = async (accountAddress) => {
  const md5Random = crypto
    .createHash("md5")
    .update(accountAddress)
    .digest("hex");

  const agent = new HttpsProxyAgent(
    `http://********-zone-custom:<EMAIL>:6200`,
    {
      timeout: 20000,
    }
  );

  fetch(`https://faucet-api.apr.io/api/claim`, {
    headers: {
      accept: "*/*",
      "accept-language": "en-US,en;q=0.9",
      "cache-control": "application/json",
      ISCHECKURLRISK: "false",
      pragma: "no-cache",
      Referer: "https://stake.apr.io/",
      "content-type": "https://stake.apr.io/",
      "Referrer-Policy": "strict-origin-when-cross-origin",
      "user-agent":
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    },
    method: "POST",
    body: JSON.stringify({ address: accountAddress }),
    agent: agent,
  })
    .then((response) => response.text())
    .then((result) => console.log(accountAddress, result))
    .catch((error) => console.error(accountAddress, error));
};

const processAccount = async (privateKey) => {
  try {
    let accountPriv = privateKey;
    let account = privateKeyToAccount(accountPriv);
    console.log(`Process pk ${accountPriv}`);
    console.log(`Process account ${account.address}`);
    console.log("get fauceet");
    await faucetGet(account.address);
    accountDone.push(accountPriv);
  } catch (error) {
    console.log("process account error", error);
  }
};

const getFaucet = async () => {
  try {
    const totalAccount = pks.length;
    const prksShuffled = shuffleArray(pks);

    let i = 0;
    let promiseAll = [];
    while (i < totalAccount) {
      if (promiseAll.length == 5000000) {
        await Promise.all(promiseAll);
        promiseAll = [];
      }
      promiseAll.push(processAccount(prksShuffled[i]));
      i++;
    }
    await Promise.all(promiseAll);
  } catch (error) {
    console.log(error, "get faucet error");
  } finally {
    fs.writeFile("./accounts2", JSON.stringify(accountDone), function (err) {
      if (err) {
        return console.log(err);
      }
      console.log("The file was saved!");
    });
  }
};

const getBalance = async () => {
  try {
    const balance = await publicClient.getBalance({
      address: "0xD7a24d1F1435CD314E86736E139f8431D4498D4e",
    });
    console.log(balance, "balance");
    return balance;
  } catch (error) {
    console.error("Error fetching balance:", error);
    return null;
  }
};

const monitorBalance = async () => {
  const newBalance = await getBalance();

  if (
    newBalance !== null &&
    lastBalance !== null &&
    newBalance !== lastBalance
  ) {
    console.log(newBalance, "newBalance");
    console.log(lastBalance, "lastBalance");
    const balanceChange = BigNumber(newBalance)
      .minus(lastBalance)
      .div(1e18)
      .toFixed();
    const message = `Change: ${balanceChange} MON`;
    console.log(message, "message");

    getFaucet();
    try {
      await bot.sendMessage(TELEGRAM_CHAT_ID, `starting get faucet`);
    } catch (error) {
      console.log(error, "balance error");
    }
    clearInterval(intervalBalance);
  }

  lastBalance = newBalance;
};

getFaucet();
// Run balance check every 10 seconds
const intervalBalance = setInterval(monitorBalance, 10 * 1000);
console.log("Monitoring balance changes...");
