const {
  createWalletClient,
  parseEther,
  http,
  createPublicClient,
  getContract,
} = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { monadTestnet } = require("viem/chains");

const walletClient = createWalletClient({
  chain: monadTestnet,
  transport: http(),
  account: privateKeyToAccount(""),
});
const publicCLient = createPublicClient({
  chain: monadTestnet,
  transport: http(),
});

const recipient = "******************************************";
const quantity = 1;

const MAGIC_CONTRACT = "******************************************";
const abi = [
  {
    inputs: [
      { internalType: "address", name: "recipient", type: "address" },
      { internalType: "uint256", name: "quantity", type: "uint256" },
    ],
    name: "mintPublic",
    stateMutability: "payable",
    type: "function",
  },
];
const magicContract = getContract({
  address: MAGIC_CONTRACT,
  abi: abi,
  client: { public: publicCLient, wallet: walletClient },
});

async function spamMint() {
  try {
    for (let i = 0; i < 10; i++) {
      const gasPrice = await publicCLient.getGasPrice();
      const hash = await magicContract.write.mintPublic(
        [recipient, BigInt(quantity)],
        { value: parseEther("0.15") }
      );

      console.log(`Mint Tx ${i + 1}: https://etherscan.io/tx/${hash}`);
    }
  } catch (error) {
    console.error("spamMint:", error);
  }
}

spamMint();
