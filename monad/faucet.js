const { generatePrivate<PERSON>ey, privateKeyToAccount } = require("viem/accounts");
const crypto = require("crypto");
const { HttpsProxyAgent } = require("https-proxy-agent");
const fs = require("fs");
const fetch = require("node-fetch");

const taskIds = [];
const accountDone = [];
function shuffleArray(array) {
  let shuffled = array.slice();
  for (let i = shuffled.length - 1; i > 0; i--) {
    let j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}
const pks = [
  "0xad82d156a533487209097853aa20ab77ca2111448aa3c3461c35147232ffe25f",
  "0x9fef97fbde889ff027af0d1f66114e03d540ac9ef5c962dc4c9f772d368f6d9a",
  "0x08bbd0568feb39aba2643c7e19dc44ec7003a33cabda6a4318b3ef5e5d764fe8",
  "0xa030c10aa19c4d2d87255a3332dc982205c14be8159c8781a53da78eefc5c080",
  "0x68e77e6f09a9563efdf95d989cb6b6cef56aed90494a8aa35cf96a3c0fb1fa2f",
  "0xf83f49b3aea14c71b8cf71ac9064f57cbc5aa9fe616792e5740dd0aa0fc8821b",
  "0xde55099a553f8f9862ff0f87a0cef13cbd1c3aa6fcde8310302fe7eda84678d0",
  "0x676bb3950920ea4ec43bbe208861eac56ff024af3e2f0cbea6e308b2277eb817",
];

let monadAccounts = require("../monadAccounts.json");
console.log(monadAccounts.length, "monadAccounts");

const generateCaptchaTask = async () => {
  const requestOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      clientKey: "48a86a60b0d6fcbec352f7081868ec04",
      task: {
        type: "TurnstileTaskProxyless",
        websiteURL: "https://testnet.monad.xyz/",
        websiteKey: "0x4AAAAAAA-3X4Nd7hf3mNGx",
      },
    }),
  };

  let res = await (
    await fetch("https://api.2captcha.com/createTask", requestOptions)
  ).json();
  taskIds.push(res.taskId);
};

const getResolveTask = async (taskId) => {
  const requestOptions = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      clientKey: "48a86a60b0d6fcbec352f7081868ec04",
      taskId: taskId,
    }),
  };

  let res = { status: "processing" };
  while (res.status != "ready") {
    res = await (
      await fetch("https://api.2captcha.com/getTaskResult", requestOptions)
    ).json();
  }

  return res;
};

const prepaidCaptcha = async (number) => {
  let promiseAll = [];
  for (let i = 0; i < number; i++) {
    promiseAll.push(generateCaptchaTask());
  }

  await Promise.all(promiseAll);
};

const faucetGet = async (accountAddress, token) => {
  const { AbortError } = await import("p-retry");

  try {
    console.log("run here");
    const md5Random = crypto
      .createHash("md5")
      .update(accountAddress)
      .digest("hex");

    const myHeaders = new Headers();
    myHeaders.append("accept", "*/*");
    myHeaders.append("accept-language", "en-US,en;q=0.9");
    myHeaders.append("cache-control", "no-cache");
    myHeaders.append("content-type", "application/json");
    myHeaders.append("origin", "https://testnet.monad.xyz");
    myHeaders.append("pragma", "no-cache");
    myHeaders.append("priority", "u=1, i");
    myHeaders.append("referer", "https://testnet.monad.xyz/");
    myHeaders.append(
      "sec-ch-ua",
      '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"'
    );
    myHeaders.append("sec-fetch-dest", "empty");
    myHeaders.append("sec-fetch-mode", "cors");
    myHeaders.append("sec-fetch-site", "same-origin");
    myHeaders.append(
      "user-agent",
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    );

    const raw = JSON.stringify({
      address: accountAddress,
      visitorId: md5Random,
      cloudFlareResponseToken: token,
    });

    const agent = new HttpsProxyAgent(
      `*************************************************************************/`,
      {
        signal: AbortSignal.timeout(20000),
      }
    );

    const requestOptions = {
      method: "POST",
      headers: myHeaders,
      body: raw,
      agent: agent,
      signal: AbortSignal.timeout(20000),
    };

    await fetch("https://testnet.monad.xyz/api/claim", requestOptions)
      .then((response) => response.text())
      .then((result) => console.log(accountAddress, result))
      .catch((error) => {
        console.error("faucet error", accountAddress, error);
        throw new AbortError("catch faucet error");
      });
  } catch (error) {
    console.log("faucet error: ", error);

    throw new AbortError("faucet error");
  }
};

const processAccount = async (privateKey) => {
  try {
    const pRetry = (await import("p-retry")).default;
    let taskId = taskIds.shift();
    let respCaptcha = await getResolveTask(taskId);
    let accountPriv = generatePrivateKey();
    // let accountPriv = privateKey;

    let account = privateKeyToAccount(accountPriv);
    console.log(`Process pk ${accountPriv}`);
    console.log(`Process account ${account.address}`);
    await pRetry(() => faucetGet(account.address, respCaptcha.solution.token), {
      retries: 5,
    });
    accountDone.push({
      privateKey: accountPriv,
      account: account.address,
    });
    monadAccounts.push(accountPriv);
  } catch (error) {
    console.log("process account error", error);
  }
};

const main = async () => {
  try {
    // const totalAccount = pks.length;
    // const pksShuffled = shuffleArray(pks);

    const totalAccount = 100;

    await prepaidCaptcha(totalAccount);
    console.log("Prepare captcha done");

    let i = 0;
    let promiseAll = [];
    while (i < totalAccount) {
      if (promiseAll.length == 5) {
        await Promise.all(promiseAll);
        promiseAll = [];
      }

      // promiseAll.push(processAccount(pksShuffled[i]));
      promiseAll.push(processAccount());
      i++;
    }
    await Promise.all(promiseAll);
  } catch (err) {
    console.log(err, "get faucet error");
  } finally {
    console.log(monadAccounts.length, "monadAccounts ended");
    fs.writeFile(
      "../monadAccounts.json",
      JSON.stringify(monadAccounts, null, 2), // formatted JSON for readability
      function (err) {
        if (err) {
          return console.log(err);
        }
        console.log("The file was saved!");
      }
    );
  }
};

main();
