const { default: axios } = require("axios");
const {
  createWalletClient,
  http,
  createPublicClient,
  getContract,
  define<PERSON>hain,
} = require("viem");
const { privateKeyToAccount } = require("viem/accounts");

const monadChain = define<PERSON>hain({
  id: 10_143,
  name: "Monad Testnet",
  nativeCurrency: {
    name: "Testnet MON Token",
    symbol: "MON",
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ["https://evm-router.magiceden.io/monad/testnet/me2024"],
    },
  },
  blockExplorers: {
    default: {
      name: "Monad Testnet explorer",
      url: "https://testnet.monadexplorer.com",
    },
  },
  contracts: {
    multicall3: {
      address: "******************************************",
      blockCreated: 251449,
    },
  },
  testnet: true,
});

const account = privateKeyToAccount(
  "0xaa846b3f45cde3ab03405287305106cac9c3f3d537678bcdbdef15b5b6d4b6d1"
);
const walletClient = createWalletClient({
  chain: monadChain,
  transport: http("https://evm-router.magiceden.io/monad/testnet/me2024"),
  account,
});
const publicCLient = createPublicClient({
  chain: monadChain,
  transport: http(),
});

const MAGIC_CONTRACT = "******************************************";

async function spamMint() {
  try {
    const balance = await publicCLient.getBalance({
      address: account.address,
    });
    console.log(balance, "balance");
    const res = await fetch(
      "https://api-mainnet.magiceden.io/v4/self_serve/nft/mint_token",
      {
        headers: {
          accept: "application/json, text/plain, */*",
          "content-type": "application/json",
          "sec-ch-ua":
            '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"Windows"',
          Referer: "https://magiceden.io/",
          "Referrer-Policy": "strict-origin-when-cross-origin",
        },
        body: `{
      "chain": "monad-testnet",
        "collectionId": "******************************************",
        "wallet": {
            "address": "******************************************",
            "chain": "monad-testnet"
        },
        "nftAmount": 1,
        "kind": "public",
        "protocol": "ERC721"
    }`,
        method: "POST",
      }
    );
    const data = await res.json();
    // const gasPrice = await publicCLient.getGasPrice();

    const params = data.steps[0].params;
    delete params.from;
    const hash = await walletClient.sendTransaction({
      ...params,
      value: BigInt(params.value),
    });

    console.log(hash, "hash");
  } catch (error) {
    console.error("spamMint:", error);
  }
}

spamMint();
