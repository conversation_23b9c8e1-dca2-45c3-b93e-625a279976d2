const { default: BigNumber } = require("bignumber.js");
const { createPublicClient, http } = require("viem");
const { privateKeyToAccount } = require("viem/accounts");
const { monadTestnet } = require("viem/chains");
const { sleep } = require("../helper");
const fs = require("fs");

const pks = [
  "0x5d82db391d9f8701751131aa0ef9c1894ca4297892acd04291a314ef02d26c46",
  "0x742e3c315bf65a212bfaf25e9e5558433e4fae37af997ec5b33763851a04f077",
  "0x61e92495cf009aebb8e0c9ff483d64b6385a4d3686194c32dd4778e1170ecdc8",
  "0x98aec623af386982ba3ab4906d5881462d8517a800621d2b4d2728a0ea11e10e",
  "0x76f88ccb1bdc2255a349e8e945637ef5fcd5e8b41e5b435bc2a4f908758180fc",
  "0xf0172e51cfedc94f02d7bbef6a40f367580374c660eb6d3ea6b4a4338c729db8",
  "0x1abfb6156305133ea7ac70f6d087c2edc7213ec7e6a27a22365bdb8164f7114f",
  "0x264269379a7ce9c2bd3e3bebd4da55ff61fa1e0148e7945dc18bd8e3290efa69",
  "0x7b1958f62303cfef50e4f7eae46ec96af39e899c388679e57b21c64a78ee6456",
  "0xad522aa2fb1fa7bf27089462425e4a08b54072e63953e6949db41816ec05cefb",
  "0x23402fc1068752625e5db3d1bf8504d71b7148dcf5ab336bcf27a0342dca819b",
  "0x132c3f4998271d40e5c8448045c260dffb155dfc823ba90e4193aa36b17f0434",
  "0x88c85ec15f1e8cdfb5ed2a2d8b766f78724f537a077c846eaa839bbfcaa863e3",
  "0xb5a25859832b28f831f67505bc87de5b6cb05d71ba677904c2685dd76ee8a35e",
  "0x7e9a27a64a34abbc3dc408f61e7ca77433b82abe3eefeab61744f9262118c4e3",
  "0x553bcb59ba506887e040dfb612bb7572cf3d314a915b41071fe0e9b9b53dc7c1",
  "0xd33f96a76245b08676854b765c7840da59037975e55c164508f49778e4e865f6",
  "0x588ef523569abb7094d0f0efbe5a272b10a84c75792cf2a6c7cffd4dda01299b",
  "0x9be6209eb76a91cc3c5b3a3ca42b109e9115959542d2508f1c193761a14c575c",
  "0xc437fd83f8540e843811d394ced3eb6c9ea3edbdd0ede3d05a3614cd73f363a7",
  "0xea73922a0d942ca5a7dc3054d44bab8c44189d2159b83865dfbb06b2a21373d7",
  "0x5f8485106bb7e9bf87b0d391f0ed301fdddb8a6ec36c7c3adc6698203346dfc4",
  "0x70b89b6ac5d865cc1ddcfda1a1f847310dcd7ded00d79a81c6fbe9214892904a",
  "0x03fc5d58af8c02c337563cdebdcbc84bf762c416b4b2151f85460dcff7f20dc4",
  "0x997f9c9c1cfba6180e4c5df5e338e71bc7237244165dbfabbd13aec7ec76c0bb",
  "0xcd9786522e4932bf4613deb2c76a9c1d208161bd218b0fd6435cc1993a2371e0",
  "0x9ba6a34e28a2996d0493a61bdcc24346e10fe32258d0f67046a7b282f34acf3b",
  "0xbd0a719ec46a5f8bbff7a24d70d8ad79a3054417edca711dc66520920cbe460d",
  "0x59a95ff4eeff2f86a23b55aca23cc5637505e620884bc60365c2057081d080a6",
  "0x443afb138283639e32e4ed7e01f1b357b754d825357e84adfa4fff1758158631",
  "0x5ede1d1176a9e6a57d5ab3b22c3d56255b99a82dca9abf44ae01fda090e79d3b",
  "0x595d424c9c211b5d610d9340908356627584b751eea198617d2f6603e80ebffc",
  "0xde1977e6c69dec1d52e74588a2b1f1970655ef35574819c5877037572ddf3df7",
  "0xbf89e29378787dbd90ad28eb227739ea75887914b67f80a9f44bf28b7fa75ed9",
  "0x3d7090e7af044c28de52b33126e8a22efd13752501d87ee11b3b0d63373b79ec",
  "0x99e196b2aab9097f324b54071e2ee99662fc646dc7d9fe4ed08cfac9f3794fd9",
  "0x47cf6477b33327cacd47dc377a1d6076c2674943857dcb5e0cdb0760fb8301e7",
  "0xa69ecd354ab9eed2084b0f3d5111d34c555c3d8249102ef852f3eb9b4e8b3a4b",
  "0x98809a1ce6e4f4dc642699930c11fbdba786cdea987281526e42a6d46c55e427",
  "0xfaa3fa4f80e4e52c75d589ace9858691deba300d71cc84fbc7184488fd2b67a6",
  "0x9fa5dec2d7833a96133d6a5fb900eccd84e8d6fb8e5d9fe5784d333ee830d6e7",
];
let totalBalance = 0;
const publicClient = createPublicClient({
  chain: monadTestnet,
  transport: http(),
});
let accounts = [];
const getBalance = async (pri) => {
  try {
    let account = privateKeyToAccount(pri);

    const balance = await publicClient.getBalance({
      address: account.address,
    });
    if (BigNumber(balance).isGreaterThan(0)) {
      accounts.push({
        privateKey: pri,
        address: account.address,
      });
      totalBalance = BigNumber(totalBalance).plus(balance).toFixed();
      console.log(BigNumber(totalBalance).div(1e18).toFixed(), "balance");
    }
  } catch (error) {
    console.error("Error fetching balance:", error);
    return null;
  }
};

const main = async () => {
  for (const pk of pks) {
    await getBalance(pk);
    await sleep(500);
  }

  fs.writeFile("./accounts", JSON.stringify(accounts), function (err) {
    if (err) {
      return console.log(err);
    }
    console.log("The file was saved!");
  });
};

main();
