const { JsonRp<PERSON><PERSON><PERSON><PERSON>, <PERSON>, RawSigner, fromExportedKeypair, toB64, Ed25519Keypair } = require("@mysten/sui.js");
require("dotenv").config({});
const { hexToBytes, bytesToHex } = require("@noble/hashes/utils");
const accounts = require("./account-v2.json");
const axios = require("axios").default;
const {  sleep } = require("../helper");

const graphqlUrl = 'https://api.flowx.finance/flowx-be/graphql'; // Replace with your GraphQL API endpoint
const provider = new JsonRpcProvider(new Connection({
  fullnode: 'https://explorer-rpc.mainnet.sui.io/'
}));
const checkInQuery = `
  mutation CheckIn($account: String!, $signature: String!) {
    checkIn(account: $account, signature: $signature) {
      success
    }
  }
`; 

const signers = [];

function initSigners() {
  for (const account of accounts) {
    const privateKey = account.privateKey;
    const keypair = Ed25519Keypair.fromSecretKey(
      hexToBytes(privateKey)
    );
    
    const signer = new RawSigner(keypair, provider);
    signers.push({
      signer,
      account: account.account
    });
  }
}

async function signMessage(signer) {
  const textEncoder = new TextEncoder();
  const message = textEncoder.encode("You are check-in day 2 referral campaign");
  const signature = await signer.signMessage({ message });

  return signature.signature;
}

async function checkIn(account, signature) {
  return axios.default({
    method: "post",
    url: graphqlUrl,
    headers: {
      "content-type": "application/json",
    },
    data: JSON.stringify({
      query: checkInQuery,
      variables: {
        account,
        signature,
      },
    }),
  });
}

async function main() {
  initSigners()

  for (const signer of signers) {
    try {
      const signature = await signMessage(signer.signer);
      const message = await checkIn(signer.account, signature);
      await sleep(2000);
      console.log("ok", signer.account);
    } catch (err) {
      console.log(err.response.data.errors, "error")
    }
  }
}


main();