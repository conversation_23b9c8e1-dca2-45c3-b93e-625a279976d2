const { BigNumberInstance, orderByKey, sleep } = require("../helper");

const { JsonRpc<PERSON>rovider, Connection, TransactionBlock, RawSigner, fromExportedKeypair, toB64, getPureSerializationType } = require("@mysten/sui.js");
require("dotenv").config({});
const { hexToBytes } = require("@noble/hashes/utils");
const { default: BigNumber } = require("bignumber.js");
const { isObject } = require("lodash");

const jsonRpcProvider = new JsonRpcProvider(new Connection({
  fullnode: 'https://explorer-rpc.mainnet.sui.io/'
}));

const account = '0x74b0615c22e2d00a50c32f91d149dec2eed0359b4caec4f75eff91057b9abe83';

const USDC = '0x5d4b302506645c37ff133b98c4b50a5ae14841659738d6d733d59d0d217a93bf::coin::COIN';
const USDT = '0xc060006111016b8a020ad5b33834984a437aaa7d3c74c18e09a95d48aceab08c::coin::COIN';
const slippage = 0.5 / 100;
const packetId = '0xba153169476e8c3114962261d1edc70de5ad9781b83cc617ecc8c1923191cae0';
const moduleName = 'router';
const functionName = 'swap_exact_input';
let count = {

}
let retryTimes = 0;

async function getCoinObjectId(coinType) {
  try {
    const result = await jsonRpcProvider.getCoins({
      owner: account,
      coinType,
    });
    const data = result.data.find(item => +item.balance > 0);
  
    return {
      objectId: data?.coinObjectId,
      balance: Number(data?.balance)
    };
  } catch (error) {
    console.log(error, "error")
    return {}
  }
}

const signer = new RawSigner(
  fromExportedKeypair({
    schema: "ED25519",
    privateKey:
      process.env.PRIVATE_KEY.length < 66
        ? toB64(fromB64(process.env.PRIVATE_KEY).slice(1))
        : toB64(
            hexToBytes(
              process.env.PRIVATE_KEY.includes("0x")
                ? process.env.PRIVATE_KEY.slice(2)
                : process.env.PRIVATE_KEY
            )
          ),
  }),
  jsonRpcProvider
);

async function swap(
  fromCoin,
  toCoin,
  outBalance
) {
  const typeArgs = [
    fromCoin,
    toCoin,
  ];
  const deadline = new Date().valueOf() + 60000;
  const {
    objectId,
    balance,
  } = await getCoinObjectId(fromCoin);

  const minimumReceived = BigNumber(balance).minus(BigNumber(balance).multipliedBy(slippage)).toFixed(0);

  if (!objectId) {
    console.log("Do not have a coin object to swap");
    return {};
  }

  const params = [
    "0x0000000000000000000000000000000000000000000000000000000000000006",
    "0xb65dcbf63fd3ad5d0ebfbf334780dc9f785eff38a4459e37ab08fa79576ee511",
    objectId,
    minimumReceived,
    account,
    deadline
  ];

  console.log(`swapping with params`);
  try {
    const tx = await formatParams(params, typeArgs);

    const estimate = await signer.devInspectTransactionBlock({
      transactionBlock: tx,
    });

    if (estimate?.effects?.status.status !== 'success') {
      console.log('Dry transaction error, wait the next tx');
      return {};
    }
    const amountXOut = estimate.events[0]?.parsedJson.amount_x_out;
    const amountYOut = estimate.events[0]?.parsedJson.amount_y_out;
    const amountOut = +amountXOut > 0 ? +amountXOut : +amountYOut;
    const amountOutFormatted = BigNumber(outBalance).minus(amountOut).dividedBy(1e6).toFixed();
    console.log(amountOutFormatted, "losing");

    if (BigNumber(amountOutFormatted).isGreaterThan(0.06) && (Number(count[amountOutFormatted] || 0) < 10)) {
      if (!count[amountOutFormatted]) {
        count[amountOutFormatted] = 1;
      } else {
        count[amountOutFormatted]++;
      }

      if (retryTimes < 5 || BigNumber(amountOutFormatted).isGreaterThan(0.17)) {
        ++retryTimes;
        return {
          ok: false,
          amountOut: 0
        };
      }
    }

    const result = await signer.signAndExecuteTransactionBlock({
      transactionBlock: tx,
    });
    count = {};
    return {
      ok: true,
      amountOut
    };
  } catch (err) {
    console.log(`swap error: ${err}`)
    return {}
  }
}

async function formatParams(params, typeArgs) {
  tx = new TransactionBlock();

  const functionDetails = await jsonRpcProvider.getNormalizedMoveModule({
    package: packetId,
    module: 'router',
  });

  const args = params?.map((param, i) => {
    return isObject(param)
      ? param
      : getPureSerializationType(functionDetails.exposedFunctions['swap_exact_input']['parameters'][i], param)
      ? tx.pure(param)
      : tx.object(param);
  }) ?? [];

  tx.moveCall({
    target: `${packetId}::${moduleName}::${functionName}`,
    typeArguments: typeArgs,
    arguments: args,
  });

  return tx;
}


async function runBot() {
  let losingBalance = 0;
  let fromCoin = USDC;
  let toCoin = USDT;
  let checkCoin = fromCoin;
  let temp = '';
  let totalVol = 0;

  const {
    objectId
  } = await getCoinObjectId(fromCoin);

  if (!objectId) {
    fromCoin = toCoin;
    toCoin = checkCoin;
    checkCoin = fromCoin;
  }

  const {
    balance
  } = await getCoinObjectId(fromCoin);
  let outBalance = 0;

  for (let i = 0; i < 1000; i++) {
    let currentBalance = 0;

    const {ok, amountOut} = await swap(fromCoin, toCoin, outBalance);
    await sleep(4000);
    if (!ok) {
      console.log("something went wrong, continue");
      continue;
    }
    console.log(`here`)
    retryTimes = 0;
    if (toCoin === checkCoin) {
      currentBalance = Number(amountOut || 0);
      losingBalance = Number(balance) - currentBalance;
      totalVol += (currentBalance) * 2;
      outBalance = amountOut;
    }

    console.log(losingBalance / 1e6, "losingBalance");
    console.log(totalVol / 1e6, "totalVol");
    temp = fromCoin;
    fromCoin = toCoin;
    toCoin = temp;

    if (currentBalance && losingBalance / 1e6 >= 10) {
      break;
    }
  }

}

runBot();
// getCoinObjectId(USDT);