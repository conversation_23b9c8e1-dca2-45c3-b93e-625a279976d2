const { BigNumberInstance, orderByKey, sleep } = require("../helper");

const { JsonRpc<PERSON>rov<PERSON>, Connection, TransactionBlock, RawSigner, fromExportedKeypair, toB64, getPureSerializationType, fromB64, Ed25519Keypair } = require("@mysten/sui.js");
require("dotenv").config({});
const { hexToBytes } = require("@noble/hashes/utils");
const { default: BigNumber } = require("bignumber.js");
const { isObject } = require("lodash");
const accounts = require("./account-spam.json");

const jsonRpcProvider = new JsonRpcProvider(new Connection({
  fullnode: 'https://api.shinami.com/node/v1/sui_mainnet_cbd0c0856c79f36db868a2270419d732'
}));

// const jsonRpcProvider = new JsonRpcProvider(new Connection({
//   fullnode: 'https://fullnode.mainnet.sui.io/'
// }));

const USDC = '0x5d4b302506645c37ff133b98c4b50a5ae14841659738d6d733d59d0d217a93bf::coin::COIN';
const USDT = '0xc060006111016b8a020ad5b33834984a437aaa7d3c74c18e09a95d48aceab08c::coin::COIN';
const PSH = '0x3c1227010835ecf9cb8f2bf9993aa9378bb951f0b3a098de6f0ee20385e01c4a::psh::PSH';
const SUI = '0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI';
const slippage = 0.5 / 100;
const packetId = '0xba153169476e8c3114962261d1edc70de5ad9781b83cc617ecc8c1923191cae0';
const moduleName = 'router';
const functionName = 'swap_exact_input';
let count = {

}
let retryTimes = 0;

class FlowxSwap {
  constructor(account, privateKey, toAccount) {
    const keypair = Ed25519Keypair.fromSecretKey(
      hexToBytes(privateKey)
    );

    this.signer = new RawSigner(keypair, jsonRpcProvider);

    this.account = account;
    this.toAccount = toAccount;
    this.isBuyingSuccess = false;
    this.isBuying = false;
  }

  async getCoinObjectId(coinType) {
    try {
      const result = await jsonRpcProvider.getCoins({
        owner: this.account,
        coinType,
      });
      const data = result.data.find(item => +item.balance > 0);
    
      return {
        objectId: data?.coinObjectId,
        balance: Number(data?.balance)
      };
    } catch (error) {
      console.log(error, "error")
      return {}
    }
  }

  async swap(
    fromCoin,
    toCoin,
  ) {
    this.isBuying = true;

    const typeArgs = [
      fromCoin,
      toCoin,
    ];
    const deadline = new Date().valueOf() + 60000;
    // const {
    //   objectId,
    // } = await this.getCoinObjectId(fromCoin);

    // console.log(objectId, "objectId 🤢🤢")

    const objectId = "0xd73e251e3631f31605d217d9871b53b75e9aafab5c17fe9573dfa215ba45aef3";

    // const objectId = '0x79a08191d1442759101265053f5c22037918a31905937fc24dd1901a5ef20a95';

    // if (!objectId) {
    //   this.isBuying = false;
    //   throw new Error("Do not have a coin object to swap")
    // }

    const params = [
      "0x0000000000000000000000000000000000000000000000000000000000000006",
      "0xb65dcbf63fd3ad5d0ebfbf334780dc9f785eff38a4459e37ab08fa79576ee511",
      objectId,
      '0',
      this.account,
      deadline
    ];

    console.log(`swapping with params`);
    try {
      const tx = await this.formatParams(params, typeArgs);

      // const estimate = await this.signer.devInspectTransactionBlock({
      //   transactionBlock: tx,
      // });

      // console.log(estimate, 'estimate')

      // if (estimate?.effects?.status.status !== 'success') {
      //   console.log('Dry transaction error, wait the next tx');
      //   return {};
      // }
      const result = await this.signer.signAndExecuteTransactionBlock({
        transactionBlock: tx,
      });

      console.log(result, 'result')

    } catch (err) {
      throw err;
    } finally {
      this.isBuying = false;
    }
  }

  async forceSwap(conIn, coinOut) {
    try {
      if (this.isBuyingSuccess) {
        console.log("buy successfully");
        return;
      }
      await this.swap(conIn, coinOut);
      this.isBuyingSuccess = true;
      console.log("buy successfully 🤢🤢");
    } catch (e) {
      console.log("forceSwap error", e)
    }
  }

  async formatParams(params, typeArgs) {
    const tx = new TransactionBlock();

    const functionDetails = await jsonRpcProvider.getNormalizedMoveModule({
      package: packetId,
      module: 'router',
    });

    const args = params?.map((param, i) => {
      return isObject(param)
        ? param
        : getPureSerializationType(functionDetails.exposedFunctions['swap_exact_input']['parameters'][i], param)
        ? tx.pure(param)
        : tx.object(param);
    }) ?? [];

    tx.moveCall({
      target: `${packetId}::${moduleName}::${functionName}`,
      typeArguments: typeArgs,
      arguments: args,
    });

    return tx;
  }

  async sendCoinTo() {
    const tx = new TransactionBlock();
    const {
      objectId
    } = await this.getCoinObjectId(USDC);
    // const suiObject = await this.getCoinObjectId("0x2::sui::SUI");
    const validAccounts = accounts.map(item => item.account)
    if  (!validAccounts.includes(this.toAccount)) {
      console.log("invalid account: " + this.toAccount)
      return;
    }

    console.log("send to account: " + this.toAccount)

    tx.transferObjects([tx.object(objectId)], tx.pure(this.toAccount))
    // const [coin] = tx.splitCoins(tx.gas, [tx.pure(suiObject.balance - *********)]);
    // tx.transferObjects([coin], tx.pure(this.toAccount))

    const result = await this.signer.signAndExecuteTransactionBlock({
      transactionBlock: tx,
    });

    console.log(result, `Send from ${this.account} to ${this.toAccount} success`)
  }
}


// const fukingSpam = async () => {
//   for (let i = 0; i < accounts.length - 2; i++) {
//     console.log("start account", accounts[i].account)
//     const flowxSwap = new FlowxSwap(accounts[i].account, accounts[i].privateKey, accounts[i +1 ].account);
//     await flowxSwap.runBot();
//     await sleep(10000);
//   }
// }

// fukingSpam()


// const flowxSwap = new FlowxSwap(
//   "0xae9bdf0fad6980b1d63152e3b80dd99eb798a45005b7b0a8231f5297bba7a9ac",
//   "16b9d30ae049837a3d00a0c4caec188f649d48e00f6fbfb4fbf3039603670d3e",
//   "0xae9bdf0fad6980b1d63152e3b80dd99eb798a45005b7b0a8231f5297bba7a9ac"
// );

// const flowxSwap = new FlowxSwap(
//   "0x9b245c6af9acde8d8682189b841a4f517e3ab9b3878334df7b4a43b14c563246",
//   "0bfd888b60e2f1f55fd3eed9589951f11404405b391c4d012407e0b7abbf1645",
//   "0x9b245c6af9acde8d8682189b841a4f517e3ab9b3878334df7b4a43b14c563246"
// );

const flowxSwap = new FlowxSwap(
  "0x5a96d6b912b262d5e3a1fc09036eabede6cf01b1ca920b2ef3005d2d79077c8a",
  "45fdf57931dfe53d85eb2a1613ebfc828278578d7b566fa4ffc98b17a6b33fbe",
  "0x5a96d6b912b262d5e3a1fc09036eabede6cf01b1ca920b2ef3005d2d79077c8a"
);

flowxSwap.forceSwap(SUI, PSH);

setInterval(() => {
  flowxSwap.forceSwap(SUI, PSH);
}, 5000)

