const bip39  = require("@scure/bip39");
const { wordlist } = require("@scure/bip39/wordlists/english");
const { DEFAULT_ED25519_DERIVATION_PATH, Ed25519Keypair } = require("@mysten/sui.js");
const fs = require('fs');

const accounts = [];
for (let i = 0; i < 300; i++) {
  const mnemonic = bip39.generateMnemonic(wordlist);
  const keypair = Ed25519Keypair.deriveKeypair(mnemonic, DEFAULT_ED25519_DERIVATION_PATH);
  const binaryPrivateKey = Uint8Array.from(atob(keypair.export().privateKey), c => c.charCodeAt(0));
  const hexadecimalPrivateKey = Array.from(binaryPrivateKey).map(byte => byte.toString(16).padStart(2, '0')).join('');

  const publicKey = keypair.getPublicKey();
  const account = publicKey.toSuiAddress();

  accounts.push({
    account,
    privateKey: hexadecimalPrivateKey
  })
}

console.log(JSON.stringify(accounts), "JSON.stringify(accounts)")
// fs.writeFile("/account-v2.json", JSON.stringify(accounts), function(err) {
//   if(err) {
//       return console.log(err);
//   }
//   console.log("The file was saved!");
// }); 