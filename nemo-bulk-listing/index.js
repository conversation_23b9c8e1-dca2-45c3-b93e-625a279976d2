require("dotenv").config({});
const whitelist = require("../data/whitelist.json");
const {hexToBytes} =  require("@noble/hashes/utils");
const {
  Connection,
  JsonRpc<PERSON>rovider,
  RawSigner,
  TransactionBlock,
  fromB64,
  fromExportedKeypair,
  getPureSerializationType,
  testnetConnection,
  toB64,
} =  require( "@mysten/sui.js");

const jsonRpcProvider = new JsonRpcProvider(new Connection({
  fullnode: process.env.JSON_RPC_ENDPOINT
}));
const signer = new RawSigner(
  fromExportedKeypair({
    schema: "ED25519",
    privateKey:
      process.env.PRIVATE_KEY.length < 66
        ? toB64(fromB64(process.env.PRIVATE_KEY).slice(1))
        : toB64(
            hexToBytes(
              process.env.PRIVATE_KEY.includes("0x")
                ? process.env.PRIVATE_KEY.slice(2)
                : process.env.PRIVATE_KEY
            )
          ),
  }),
  jsonRpc<PERSON>rovider
);
const DEFAULT_PAGINATION_LIMIT = 1000;

export const fetchWhitelisted = async () => {
  console.log("start fetching whitelisted ...");
  let hasNextPage = false;
  let cursor = null;
  const whitelisted = [];
  do {
    const res = await jsonRpcProvider.getDynamicFields({
      parentId: process.env.WHITELIST_DYNAMIC_FIELD_OBJECT_ID,
      cursor,
      limit: DEFAULT_PAGINATION_LIMIT,
    });
    hasNextPage = res.hasNextPage;
    cursor = res.nextCursor;
    whitelisted.push(...res.data.map((data) => data.name.value));
  } while (!!hasNextPage);

  return whitelisted;
};

export const prepareImport = async (addresses) => {
  addresses = [...new Set(addresses)];
  const objects = await jsonRpcProvider.multiGetObjects({
    ids: addresses,
  });
  return objects.filter((obj) => !!obj.error).map((obj) => obj.error.object_id);
};

export const importWhitelist = async () => {
  let totalValidAddresses = 0;
  const whitelisted = await fetchWhitelisted();
  const unWhitelisted = whitelist.filter((item) => !whitelisted.includes(item));

  console.log("start importing whitelist ...");
  const CHUNK = 50;
  let numTxs = Math.ceil(unWhitelisted.length / CHUNK);
  let index = 0;
  while (index < numTxs) {
    const txb = new TransactionBlock();
    const validAddresses = await prepareImport([...new Set(unWhitelisted.slice(index * CHUNK, (index + 1) * CHUNK))]);
    totalValidAddresses += validAddresses.length;
    console.log("CAMPAIGN_PACKAGE_OBJECT_ID", process.env.CAMPAIGN_PACKAGE_OBJECT_ID);
    console.log("CAMPAIGN_ADMIN_OBJECT_ID", process.env.CAMPAIGN_ADMIN_OBJECT_ID);
    console.log("CAMPAIGN_SHARED_OBJECT_ID", process.env.CAMPAIGN_SHARED_OBJECT_ID);

    txb.moveCall({
      target: `${process.env.CAMPAIGN_PACKAGE_OBJECT_ID}::flowx_campaign::whitelist`,
      arguments: [
        txb.object(process.env.CAMPAIGN_ADMIN_OBJECT_ID),
        txb.object(process.env.CAMPAIGN_SHARED_OBJECT_ID),
        txb.makeMoveVec({
          objects: validAddresses.map((item, i) => txb.pure(utf8Encode.encode("abc"), "vector<u8>")),
        }),
      ],
      typeArguments: [],
    });
    const result = await signer.devInspectTransactionBlock({
      transactionBlock: txb,
    });
    console.log("result", JSON.stringify(result));
  }

  console.log("total valid addresses", totalValidAddresses);
};

(async function () {
  console.log("signer", await signer.getAddress());
  importWhitelist();
})();