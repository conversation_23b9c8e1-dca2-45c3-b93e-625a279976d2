const axios = require("axios").default;

const nftAssetsOfQuery = `
  query NftAssetsOf($account: String!) {
    nftAssetsOf(account: $account) {
      tokenId
      tokenContract
      name
      imageUrl
    }
  }
`;

const refreshMetadataMutation = `
  mutation ResyncMetadata($tokenContract: String!, $tokenId: Float!) {
    resyncMetadata(tokenContract: $tokenContract, tokenId: $tokenId) {
      tokenId
      name
    }
  }
`;

function getNemoNft() {
  return axios.default({
    method: "post",
    url: `https://stg.nemo-dev.com/nemo-be/graphql`,
    headers: {
      "content-type": "application/json",
      "X-BLOCKCHAIN-ID": "bsc",
    },
    data: JSON.stringify({
      query: nftAssetsOfQuery,
      variables: {
        account: "******************************************",
      },
    }),
  });
}

function refreshMetadata(tokenContract, tokenId) {
  return axios.default({
    method: "post",
    url: `https://stg.nemo-dev.com/nemo-be/graphql`,
    headers: {
      "content-type": "application/json",
      "X-BLOCKCHAIN-ID": "bsc",
    },
    data: JSON.stringify({
      query: refreshMetadataMutation,
      variables: {
        tokenContract,
        tokenId,
      },
    }),
  });
}

async function forceRefreshMetadata() {
  let nftNull = 0;
  console.log("getting NFT");
  getNemoNft()
    .then(async (data) => {
      let NFTs = data.data.data.nftAssetsOf;
      console.log("total NFT: ", NFTs.length);

      for (let i = 0; i < NFTs.length; i++) {
        if (NFTs[i].name && !NFTs[i].imageUrl.includes("default")) {
          continue;
        }

        nftNull++;
        const data = await refreshMetadata(
          NFTs[i].tokenContract,
          NFTs[i].tokenId
        );

        if (data.data.errors) {
          console.log(
            `refreshed NFT error: ${JSON.stringify(data.data.errors)}`
          );
          continue;
        }

        console.log("refreshed nft", data.data);
      }

      console.log(`refresh ${nftNull} NFT`);
    })
    .catch((e) => {
      console.log(e);
    });
}

forceRefreshMetadata();
