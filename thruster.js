const { ethers } = require("ethers");
const swapABI = require('./abi/swap.json')
const { default: BigNumber } = require('bignumber.js')

const privateKey = "";
const ADDRESS = '';
const ROUTER_ADDRESS = '******************************************'
const BLAST_TOKEN = '******************************************'
const BLAST_TOKEN_ABI = [{
  "inputs": [{
    "internalType": "address",
    "name": "account",
    "type": "address"
  }],
  "name": "balanceOf",
  "outputs": [{
    "internalType": "uint256",
    "name": "",
    "type": "uint256"
  }],
  "stateMutability": "view",
  "type": "function"
}]

const provider = new ethers.providers.JsonRpcProvider("https://rpc.blast.io");
const wallet = new ethers.Wallet(privateKey, provider);
const blastTokenContract = new ethers.Contract(BLAST_TOKEN, BLAST_TOKEN_ABI, wallet);
const contract = new ethers.Contract(ROUTER_ADDRESS, swapABI, wallet);
const amount = ethers.utils.parseEther('5');
const deadline = **********;
const amountOutMinimum = ethers.utils.parseEther('0.*****************')

const swap = async () => {
  try {
    const balanceOf = BigNumber((await blastTokenContract.balanceOf(ADDRESS)).toString()).div(1e18).toFixed();

    // const tx = await contract.exactInputSingle({
    //   tokenIn: '******************************************',
    //   tokenOut: '******************************************',
    //   fee: 3000,
    //   recipient: address,
    //   deadline,
    //   amountIn: amount,
    //   amountOutMinimum,
    //   sqrtPriceLimitX96: 0
    // })

    // await tx.wait();
    console.log(tx.hash, 'tx');
  } catch (err) {
    console.log(err, "error")
  }
}

swap();