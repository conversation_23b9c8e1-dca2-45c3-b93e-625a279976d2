const { ethers } = require("ethers");
const swapABI = require('../abi/blaster.json')
const { default: BigNumber } = require('bignumber.js')
const {  sleep } = require("../helper");
const { abi: MulticallABI } = require("@uniswap/v3-periphery/artifacts/contracts/interfaces/IMulticall.sol/IMulticall.json");
const { abi: V3SwapRouterABI } = require('@uniswap/v3-periphery/artifacts/contracts/interfaces/ISwapRouter.sol/ISwapRouter.json')
const { abi: PeripheryPaymentsABI } = require("@uniswap/v3-periphery/artifacts/contracts/interfaces/IPeripheryPayments.sol/IPeripheryPayments.json");

const privateKey = "";
const ADDRESS = '******************************************';
const ROUTER_ADDRESS = '******************************************'
const USDB_TOKEN = '******************************************'
const WETH_TOKEN = '******************************************'
const TOKEN_ABI = [{
  "inputs": [{
    "internalType": "address",
    "name": "account",
    "type": "address"
  }],
  "name": "balanceOf",
  "outputs": [{
    "internalType": "uint256",
    "name": "",
    "type": "uint256"
  }],
  "stateMutability": "view",
  "type": "function"
}]

const provider = new ethers.providers.JsonRpcProvider("https://rpc.blast.io");
const wallet = new ethers.Wallet(privateKey, provider);
const usdbContract = new ethers.Contract(USDB_TOKEN, TOKEN_ABI, wallet);
const wethContract = new ethers.Contract(WETH_TOKEN, TOKEN_ABI, wallet);
const contract = new ethers.Contract(ROUTER_ADDRESS, V3SwapRouterABI.concat(PeripheryPaymentsABI).concat(MulticallABI), wallet);
const amount = ethers.utils.parseEther('5');
const deadline = **********;
let vol = 0;

const swap = async (tokenIn, tokenOut, amount, sqrt) => {
  const exactInputSingleCall = contract.interface.encodeFunctionData("exactInputSingle", [{
    tokenIn: tokenIn,
    tokenOut: tokenOut,
    fee: 1,
    recipient: ADDRESS,
    deadline,
    amountIn: amount,
    amountOutMinimum: 0,
    sqrtPriceLimitX96: sqrt
  }]);
  const multicallData = contract.interface.encodeFunctionData("multicall", [[exactInputSingleCall]]);

  const tx = await wallet.estimateGas({
    to: ROUTER_ADDRESS,
    data: multicallData,
    from: ADDRESS
});


//   const tx = await contract.multicall([
//     tokenIn,
//     tokenOut,
//     3000,
//     ADDRESS,
//     deadline,
//     amount,
//     0,
//     0
// ]);
  console.log(tx, 'tx')
    // await sleep(Math.floor(Math.random(10000, 600000)));
    // handleTradingVolume();
    return tx.hash;
}

const handleTradingVolume =  async () => {
  try {
    console.log("start")
    const usdbBalance = BigNumber((await usdbContract.balanceOf(ADDRESS)).toString()).toFixed();
    console.log(usdbBalance, 'usdbBalance')
    const wethBalance = BigNumber((await wethContract.balanceOf(ADDRESS)).toString()).toFixed();
    console.log(wethBalance, 'wethBalance')
    let amount = usdbBalance;
    let tokenIn = USDB_TOKEN;
    let tokenOut = WETH_TOKEN;
    let sqrt = calculateSqrt(3343000000000000000000);

    if (amount <= '2035996930393') {
      amount = wethBalance;
      tokenIn = WETH_TOKEN;
      tokenOut = USDB_TOKEN;
      sqrt = calculateSqrt(301000000000000)
    }

    console.log(sqrt, 'sqrt')

    console.log("start swap amount", {
      wethBalance,
      usdbBalance,
      tokenIn
    })

    const tx = await swap(tokenIn, tokenOut, amount, sqrt)
    console.log("swap success", tx);
  } catch (err) {
    console.log("swap error")
  }
}

const calculateSqrt = (price) => {
    const sqrtPrice = Math.sqrt(price);

    return BigNumber(sqrtPrice * Math.pow(2, 96)).toString();
}

handleTradingVolume();