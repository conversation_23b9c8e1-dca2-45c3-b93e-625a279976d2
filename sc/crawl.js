const { ethers } = require("ethers");
const {isZeroAddress, Account}= require("ethereumjs-util");
const { formatEther } = require("ethers/lib/utils");

const provider = new ethers.providers.JsonRpcProvider("https://bsc.blockpi.network/v1/rpc/public");
const abi = ["event Transfer(address indexed from, address indexed to, uint256 value)"];
const currentCrawl = {};
const previousCrawl = {};

async  function crawlBlocks(topic, block, contractAddresses = [], crawlChecking = {}) {
  try {
    // const iface = new ethers.utils.Interface(abi);
    const events = await provider.getLogs({ fromBlock: block.fromBlock, toBlock: block.toBlock });

    const results = events.filter(event => event.topics[0] === topic && 
      contractAddresses.includes(event.address.toLowerCase()),
    );
    console.log(results, "results")
    results.map(item => console.log(item, "item"))
    
    crawlChecking[block.fromBlock] = {
      fromBlock: block.fromBlock,
      toBlock: block.toBlock,
      eventLength: results.length
    }
  } catch (error) {
     console.log(error, "error");
  }
}

let block1 = 26707100;
let block2 = 22230597;


//  crawlBlocks("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822", {
//   fromBlock: block1,
//   toBlock: block1 = block1 + 10,
// }, ["******************************************"], currentCrawl);

// setInterval(async () => {
//   await crawlBlocks("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822", {
//     fromBlock: block1,
//     toBlock: ++block1,
//   }, ["******************************************"], currentCrawl);
// }, 1000);

// setInterval(async () => {
//   await crawlBlocks("0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822", {
//     fromBlock: block2,
//     toBlock: ++block2,
//   }, ["******************************************"], previousCrawl);

  
//   Object.keys(previousCrawl).forEach(block => {
//     if (!previousCrawl[block]) {
//       return;
//     }
    
//     if (currentCrawl[block].eventLength !== previousCrawl[block].eventLength) {
//       throw Error(`crawl missing event in block ${block} `);
//     }
    
//     console.log("crawl block ok", block)
//   })
  
// }, 50000);


console.log("starting...")

async function listenEvent() {
  const idContract = new ethers.Contract("******************************************", abi, provider);

  idContract.on("Transfer", (from, to, amount, event) => {
    handleEvent(from, to, amount, event);
  });
}

async function handleEvent(lpAddress, maker, amount, event) {
  const cakeLp = ["******************************************", "******************************************"]
  console.log(event, "event")

  const code = await provider.getCode(maker);
  if (code !== '0x') {
    return;
  }

  if (cakeLp.includes(lpAddress.toLowerCase())) {
    console.log(`Address ${ maker } buy ${ formatEther(amount)}   , hash ${event.transactionHash} 💵`);
  }
}

listenEvent();