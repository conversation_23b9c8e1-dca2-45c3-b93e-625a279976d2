const { forEach, isObject } = require("lodash");

const dirtyFields = (currentParams, updatedParams) => {
  const _dirtyFields = [];

  forEach(updatedParams, (value, key) => {
    const currentValue = currentParams[key];

    if (isObject(currentValue)) {
      // If the value is an object, recursively check for dirty fields
      const nestedDirtyFields = dirtyFields(currentValue, value);
      _dirtyFields.push(...nestedDirtyFields.map((field) => `${key}.${field}`));
    } else if (value !== currentValue) {
      _dirtyFields.push(key);
    }
  });

  return _dirtyFields;
};

const arrIncludesAny = (arr, checkArr) => {
  return arr.some(item => checkArr.includes(item));
};

let oldObject = {
  test: 2,
  config: {
    test: 2,
    vcl: 4,
  }
};

const newObject = {
  test: 1,
  config: {
    test: 3,
    vcl: 4,
  }
}

const fileds = dirtyFields(oldObject, newObject);



console.log(arrIncludesAny(fileds, ['config']));